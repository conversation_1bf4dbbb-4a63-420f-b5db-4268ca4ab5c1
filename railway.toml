# Railway configuration for DataHero4 Backend
# Single service configuration for backend deployment

[build]
builder = "dockerfile"
dockerfilePath = "apps/backend/Dockerfile"
buildCommand = "echo 'Building DataHero4 backend...'"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 30
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 10
startCommand = "python webapp_unified.py"

# Environment variables for production
[variables]
NODE_ENV = "production"
LOG_LEVEL = "DEBUG"
USE_OPTIMIZED_WORKFLOW = "true"
PYTHONUNBUFFERED = "1"
PORT = "8000"
ENVIRONMENT = "production"
FORCE_REBUILD = "v7.0"
