import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Star, 
  MoreVertical,
  Trash2,
  AlertCircle
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ResponsiveContainer, 
  XAxis, 
  YAxis, 
  Tooltip,
  CartesianGrid
} from 'recharts';
import { KpiData } from '@/hooks/useKpiData';
import { cn } from '@/lib/utils';
import { kpiEvents } from '@/lib/events';
import { type CurrencyOption } from '@/hooks/useDashboardFilters';

interface KpiBentoCardProps {
  kpi: KpiData;
  currency: CurrencyOption;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  isLarge?: boolean;
  periodData?: {
    currentDate: string;
    isSimulated: boolean;
    periodDescription?: string | null;
  };
}

const KpiBentoCard: React.FC<KpiBentoCardProps> = ({
  kpi,
  currency,
  onTogglePriority,
  onRemoveKpi,
  isLarge = false,
  periodData
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const formatValue = (value: number) => {
    if (kpi.format === 'currency') {
      return value.toLocaleString('pt-BR', { 
        style: 'currency', 
        currency: currency === 'all' ? 'BRL' : currency.toUpperCase(),
        minimumFractionDigits: 0,
        maximumFractionDigits: value < 1000 ? 2 : 0
      });
    } else if (kpi.format === 'percentage') {
      return `${value.toFixed(2)}%`;
    } else {
      return value.toLocaleString('pt-BR', { 
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    }
  };

  const getTrendIcon = () => {
    if (!kpi.changePercent) return null;
    
    const Icon = kpi.changePercent > 0 ? TrendingUp : TrendingDown;
    const color = kpi.changePercent > 0 ? 'text-green-600' : 'text-red-600';
    
    return (
      <div className={cn("flex items-center gap-1", color)}>
        <Icon className="w-4 h-4" />
        <span className="text-sm font-medium">
          {kpi.changePercent > 0 ? '+' : ''}{kpi.changePercent.toFixed(1)}%
        </span>
      </div>
    );
  };

  const renderChart = () => {
    if (!kpi.chartData || kpi.chartData.length === 0) {
      return (
        <div className="h-full flex items-center justify-center text-gray-400 text-sm">
          Sem dados históricos
        </div>
      );
    }

    // Define gradient ID for this specific chart
    const gradientId = `gradient-${kpi.id}`;

    // Custom tooltip component
    const CustomTooltip = ({ active, payload, label }: any) => {
      if (active && payload && payload.length) {
        return (
          <div className="bg-white/95 backdrop-blur-sm p-3 rounded-lg shadow-lg border border-gray-200">
            <p className="text-xs text-gray-600 mb-1">{label}</p>
            <p className="text-sm font-semibold text-gray-900">
              {formatValue(payload[0].value)}
            </p>
          </div>
        );
      }
      return null;
    };

    return (
      <div className="h-full -mx-2 -mb-6">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={kpi.chartData} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#374151" stopOpacity={0.15} />
                <stop offset="25%" stopColor="#4B5563" stopOpacity={0.12} />
                <stop offset="50%" stopColor="#6B7280" stopOpacity={0.08} />
                <stop offset="75%" stopColor="#9CA3AF" stopOpacity={0.05} />
                <stop offset="100%" stopColor="#D1D5DB" stopOpacity={0.02} />
              </linearGradient>
            </defs>
            <XAxis dataKey="name" hide />
            <YAxis hide />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="value"
              stroke="#000000"
              strokeWidth={3}
              fill={`url(#${gradientId})`}
              fillOpacity={1}
              connectNulls={false}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    );
  };

  const cardContent = (
    <div className="relative h-full flex flex-col p-6">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className={cn(
            "font-semibold text-gray-900",
            isLarge ? "text-xl" : "text-lg"
          )}>
            {kpi.title}
          </h3>
          <p className={cn(
            "text-gray-500 mt-1",
            isLarge ? "text-sm" : "text-xs"
          )}>
            {kpi.description}
          </p>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <MoreVertical className="h-4 w-4 text-gray-500" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onTogglePriority(kpi.id)}>
              <Star className={cn(
                "mr-2 h-4 w-4",
                kpi.isPriority ? "fill-amber-500 text-amber-500" : "text-gray-400"
              )} />
              {kpi.isPriority ? 'Remover prioridade' : 'Marcar como prioritário'}
            </DropdownMenuItem>
            {onRemoveKpi && (
              <DropdownMenuItem 
                onClick={() => onRemoveKpi(kpi.id)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Remover do dashboard
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Value Section */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div 
            className={cn(
              "font-bold text-gray-900",
              isLarge ? "text-4xl" : "text-2xl"
            )}
            data-testid={`${kpi.id}-value`}
          >
            {formatValue(kpi.currentValue)}
          </div>
          {getTrendIcon()}
        </div>
      </div>

      {/* Chart Section */}
      <div className={cn(
        "flex-1 min-h-0",
        isLarge ? "h-40" : "h-20"
      )}>
        {renderChart()}
      </div>
    </div>
  );

  return (
    <motion.div
      className="h-full"
      whileHover={{ scale: 1.02 }}
      transition={{ type: "spring", stiffness: 300 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card 
        className={cn(
          "h-full relative overflow-hidden transition-all duration-300",
          "bg-white",
          "hover:shadow-xl cursor-pointer",
          isHovered && "shadow-xl",
          // Alert styling with yellow border
          kpi.alert ? "border-amber-400 border-2" : "border-gray-200"
        )}
        data-testid="kpi-card"
        data-kpi-id={kpi.id}
      onClick={(e) => {
        const cardElement = e.currentTarget as HTMLElement;
        const rect = cardElement.getBoundingClientRect();
        kpiEvents.selectKPI({ 
          kpiId: kpi.id, 
          element: cardElement,
          cardRect: rect 
        });
      }}
      >
        {cardContent}
      </Card>
    </motion.div>
  );
};

export default KpiBentoCard;