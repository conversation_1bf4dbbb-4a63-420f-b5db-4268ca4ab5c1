import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { KpiData } from '@/hooks/useKpiData';
import KpiBentoCard from './KpiBentoCard';
import { type CurrencyOption } from '@/hooks/useDashboardFilters';

interface KpiBentoGridProps {
  kpis: KpiData[];
  currency: CurrencyOption;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  periodData?: {
    currentDate: string;
    isSimulated: boolean;
    periodDescription?: string | null;
  };
}

const KpiBentoGrid: React.FC<KpiBentoGridProps> = ({
  kpis,
  currency,
  onTogglePriority,
  onRemoveKpi,
  periodData
}) => {
  // Define grid layout for 6 critical KPIs in bento style
  const getGridClassName = (index: number) => {
    // Create a 12-column grid layout
    // First row: 2 large cards (6 cols each)
    // Second row: 4 medium cards (3 cols each)
    switch (index) {
      case 0: // Volume Total - Large, top left
        return "col-span-12 md:col-span-6 row-span-2";
      case 1: // Ticket Médio - Large, top right
        return "col-span-12 md:col-span-6 row-span-2";
      case 2: // Spread Médio - Medium
        return "col-span-6 md:col-span-3 row-span-1";
      case 3: // Taxa de Conversão - Medium
        return "col-span-6 md:col-span-3 row-span-1";
      case 4: // Taxa de Retenção - Medium
        return "col-span-6 md:col-span-3 row-span-1";
      case 5: // Margem Bruta - Medium
        return "col-span-6 md:col-span-3 row-span-1";
      default:
        return "col-span-6 md:col-span-4 row-span-1";
    }
  };

  return (
    <div className="w-full px-4 md:px-6 lg:px-8">
      <motion.div 
        className="grid grid-cols-12 gap-4 md:gap-6 auto-rows-[minmax(180px,_1fr)]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {kpis.map((kpi, index) => (
          <motion.div
            key={kpi.id}
            className={cn(
              getGridClassName(index),
              "min-h-0" // Allow cards to size based on content
            )}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ 
              duration: 0.5, 
              delay: index * 0.1,
              ease: "easeOut"
            }}
          >
            <KpiBentoCard
              kpi={kpi}
              currency={currency}
              onTogglePriority={onTogglePriority}
              onRemoveKpi={onRemoveKpi}
              isLarge={index < 2}
              periodData={periodData}
            />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default KpiBentoGrid;