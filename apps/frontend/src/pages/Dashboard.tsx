import React, { useState, useEffect } from 'react';
import { MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { getDashboardSnapshot, convertSnapshotToKpiData, type KpiData } from '@/lib/api';
import KpiCard from '@/components/dashboard/KpiCard';
import KpiBentoGrid from '@/components/dashboard/KpiBentoGrid';
import DashboardControls from '@/components/dashboard/DashboardControls';
import AddKpiModal from '@/components/dashboard/AddKpiModal';
import { KPIDrawer } from '@/components/kpi-drawer';
import { DashboardLoading, FilterLoading } from '@/components/dashboard/LoadingStates';
import { useKpiData } from '@/hooks/useKpiData';
import { useDashboardFilters } from '@/hooks/useDashboardFilters';

const Dashboard = () => {
  const navigate = useNavigate();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showAddKpiModal, setShowAddKpiModal] = useState(false);
  
  // Dashboard filters state
  const { filters, updateTimeframe, updateCurrency } = useDashboardFilters();
  
  // KPI data with filters
  const { kpis, isLoading, isInitialLoading, isFilterChanging, isRefreshing: isKpisRefreshing, error, togglePriority, refreshKpis } = useKpiData(filters);

  console.log('Dashboard component rendering');

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshKpis();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleExport = () => {
    // Implementar lógica de exportação
    console.log('Exportando dados...');
  };

  const handleAddKpi = () => {
    setShowAddKpiModal(true);
  };

  const handleKpisSelected = async (kpiIds: string[]) => {
    try {
      // Por enquanto, apenas fechar o modal
      console.log(`✅ ${kpiIds.length} KPIs selecionados:`, kpiIds);
      setShowAddKpiModal(false);
    } catch (error) {
      console.error('❌ Erro ao adicionar KPIs:', error);
    }
  };

  const handleRemoveKpi = (kpiId: string) => {
    console.log(`🗑️ KPI ${kpiId} será removido`);
  };

  // Lista dos IDs dos KPIs existentes para o modal
  const existingKpiIds: string[] = [];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header simples sem linha separadora */}
      <div className="bg-gray-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {/* Ícone do DataHero */}
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            
            {/* Indicadores de status */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Sistema Online</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span>Atualizando</span>
              </div>
            </div>
          </div>
          
          {/* Botão para ir às perguntas */}
          <Button 
            variant="outline" 
            onClick={() => navigate('/')}
            className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <MessageSquare className="w-4 h-4" />
            Fazer Perguntas
          </Button>
        </div>
      </div>

      {/* Conteúdo principal com melhor espaçamento */}
      <div className="pb-8">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {/* Título e descrição */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Métricas Críticas</h1>
            <p className="mt-2 text-gray-600">
              6 indicadores essenciais para acompanhamento executivo em tempo real
            </p>
          </div>

          {/* Controles do Dashboard */}
          <DashboardControls 
            filters={filters}
            onTimeframeChange={updateTimeframe}
            onCurrencyChange={updateCurrency}
            onRefresh={handleRefresh}
            onExport={handleExport}
            onAddKpi={handleAddKpi}
            isRefreshing={isRefreshing}
          />

          {/* Loading indicator when filters change */}
          {isFilterChanging && (
            <div className="mb-4">
              <FilterLoading />
            </div>
          )}

          {/* Grid de KPIs com Bento Layout */}
          {isInitialLoading ? (
            <DashboardLoading />
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Erro ao carregar KPIs
              </h3>
              <p className="text-red-600 mb-4">{error}</p>
              <Button 
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isRefreshing ? 'Carregando...' : 'Tentar novamente'}
              </Button>
            </div>
          ) : (
            <KpiBentoGrid 
              kpis={kpis}
              currency={filters.currency}
              onTogglePriority={togglePriority}
              onRemoveKpi={handleRemoveKpi}
              periodData={{
                currentDate: new Date().toISOString(),
                isSimulated: false
              }}
            />
          )}
        </div>
      </div>

      {/* Modal para adicionar KPIs */}
      <AddKpiModal
        isOpen={showAddKpiModal}
        onClose={() => setShowAddKpiModal(false)}
        onKpisSelected={handleKpisSelected}
        existingKpiIds={[]}
      />
      
      {/* KPI Drawer */}
      <KPIDrawer />
    </div>
  );
};

export default Dashboard;
