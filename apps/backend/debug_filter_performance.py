#!/usr/bin/env python3
"""
Debug script para investigar por que o dashboard demora tanto para atualizar quando muda filtros.
"""

import os
import sys
import time
import asyncio
import requests
from pathlib import Path

# Adicionar o diretório raiz ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Carregar variáveis de ambiente
from dotenv import load_dotenv
load_dotenv()

def test_api_performance():
    """Testa a performance da API de KPIs com diferentes filtros."""
    print("🔍 TESTANDO PERFORMANCE DA API DE KPIS")
    print("=" * 50)
    
    base_url = "http://localhost:8000/api/dashboard/kpis"
    
    # Diferentes combinações de filtros para testar
    test_cases = [
        {"timeframe": "week", "currency": "all", "name": "Semana + Todas moedas"},
        {"timeframe": "month", "currency": "all", "name": "<PERSON><PERSON><PERSON> + <PERSON>das moedas"},
        {"timeframe": "week", "currency": "usd", "name": "Semana + USD"},
        {"timeframe": "week", "currency": "eur", "name": "Semana + EUR"},
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📊 Teste {i}/4: {test_case['name']}")
        print("-" * 40)
        
        params = {
            "sector": "cambio",
            "client_id": "L2M",
            "timeframe": test_case["timeframe"],
            "currency": test_case["currency"],
            "priority_only": "true"
        }
        
        # Primeira chamada (cache miss esperado)
        print("🔄 Primeira chamada (cache miss)...")
        start_time = time.time()
        
        try:
            response = requests.get(base_url, params=params, timeout=30)
            first_call_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                kpi_count = len(data.get("kpis", []))
                print(f"✅ Sucesso: {kpi_count} KPIs em {first_call_time:.0f}ms")
                
                # Verificar se tem informações de cache
                cache_info = data.get("cache_status", "unknown")
                processing_time = data.get("processing_time_ms", "unknown")
                print(f"   Cache: {cache_info}, Processing: {processing_time}ms")
                
            else:
                print(f"❌ Erro HTTP {response.status_code}: {response.text}")
                first_call_time = None
                
        except requests.exceptions.Timeout:
            print("⏰ Timeout (>30s)")
            first_call_time = None
        except Exception as e:
            print(f"❌ Erro: {e}")
            first_call_time = None
        
        # Segunda chamada (cache hit esperado)
        print("🎯 Segunda chamada (cache hit esperado)...")
        start_time = time.time()
        
        try:
            response = requests.get(base_url, params=params, timeout=30)
            second_call_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                kpi_count = len(data.get("kpis", []))
                print(f"✅ Sucesso: {kpi_count} KPIs em {second_call_time:.0f}ms")
                
                cache_info = data.get("cache_status", "unknown")
                processing_time = data.get("processing_time_ms", "unknown")
                print(f"   Cache: {cache_info}, Processing: {processing_time}ms")
                
                # Calcular speedup
                if first_call_time and second_call_time:
                    speedup = first_call_time / second_call_time
                    print(f"🚀 Speedup: {speedup:.1f}x mais rápido")
                
            else:
                print(f"❌ Erro HTTP {response.status_code}: {response.text}")
                second_call_time = None
                
        except requests.exceptions.Timeout:
            print("⏰ Timeout (>30s)")
            second_call_time = None
        except Exception as e:
            print(f"❌ Erro: {e}")
            second_call_time = None
        
        results.append({
            "name": test_case["name"],
            "params": params,
            "first_call_ms": first_call_time,
            "second_call_ms": second_call_time
        })
        
        # Pausa entre testes
        time.sleep(2)
    
    # Resumo
    print(f"\n{'='*50}")
    print("📋 RESUMO DOS RESULTADOS")
    print(f"{'='*50}")
    
    for result in results:
        print(f"\n🔸 {result['name']}")
        if result["first_call_ms"]:
            print(f"   1ª chamada: {result['first_call_ms']:.0f}ms")
        else:
            print(f"   1ª chamada: FALHOU")
            
        if result["second_call_ms"]:
            print(f"   2ª chamada: {result['second_call_ms']:.0f}ms")
        else:
            print(f"   2ª chamada: FALHOU")
    
    # Análise
    print(f"\n💡 ANÁLISE")
    print("-" * 20)
    
    slow_calls = [r for r in results if r["first_call_ms"] and r["first_call_ms"] > 5000]
    if slow_calls:
        print(f"⚠️  {len(slow_calls)} chamadas lentas (>5s) detectadas")
        for call in slow_calls:
            print(f"   - {call['name']}: {call['first_call_ms']:.0f}ms")
    
    cache_working = [r for r in results if r["first_call_ms"] and r["second_call_ms"] and r["second_call_ms"] < r["first_call_ms"]]
    if cache_working:
        print(f"✅ Cache funcionando em {len(cache_working)} casos")
    else:
        print(f"❌ Cache não está funcionando adequadamente")


def test_redis_connection():
    """Testa se o Redis está funcionando."""
    print("\n🔌 TESTANDO CONEXÃO REDIS")
    print("=" * 30)
    
    try:
        import redis
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        client = redis.from_url(redis_url, decode_responses=True)
        
        # Ping test
        response = client.ping()
        print(f"✅ Redis ping: {response}")
        
        # Set/get test
        test_key = "debug:performance:test"
        test_value = "test_value"
        
        client.setex(test_key, 60, test_value)
        retrieved = client.get(test_key)
        
        if retrieved == test_value:
            print("✅ Redis set/get funcionando")
        else:
            print("❌ Redis set/get com problema")
            
        client.delete(test_key)
        return True
        
    except Exception as e:
        print(f"❌ Redis não está funcionando: {e}")
        return False


def check_backend_status():
    """Verifica se o backend está rodando."""
    print("\n🖥️  VERIFICANDO STATUS DO BACKEND")
    print("=" * 35)

    try:
        print("⏳ Testando conexão com timeout de 30s...")
        response = requests.get("http://localhost:8000/health", timeout=30)
        if response.status_code == 200:
            print("✅ Backend está rodando")
            return True
        else:
            print(f"❌ Backend retornou status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend não está rodando (conexão recusada)")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Backend está muito lento (timeout >30s)")
        return False
    except Exception as e:
        print(f"❌ Erro verificando backend: {e}")
        return False


def main():
    """Executa todos os testes de debug."""
    print("🐛 DEBUG: POR QUE O DASHBOARD DEMORA TANTO?")
    print("=" * 60)
    
    # 1. Verificar se backend está rodando
    if not check_backend_status():
        print("\n❌ Backend não está rodando. Execute 'poetry run uvicorn src.interfaces.api:app --reload' primeiro.")
        return
    
    # 2. Verificar Redis
    redis_ok = test_redis_connection()
    if not redis_ok:
        print("\n⚠️  Redis não está funcionando. Cache pode estar desabilitado.")
    
    # 3. Testar performance da API
    test_api_performance()
    
    print(f"\n🎯 CONCLUSÕES")
    print("=" * 15)
    print("1. Se as chamadas estão demorando >10s, o problema é no cálculo dos KPIs")
    print("2. Se o cache não está funcionando, o problema é no Redis ou configuração")
    print("3. Se ambos estão OK, o problema pode ser na query do banco de dados")
    print("\n💡 Próximos passos:")
    print("- Verificar logs do backend para erros")
    print("- Analisar queries SQL sendo executadas")
    print("- Verificar se o banco de dados está respondendo rapidamente")


if __name__ == "__main__":
    main()
