"""
Unit Tests for KpiService Enhancements
======================================

Tests for the new methods added to KpiService in this branch.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal
from datetime import datetime

from src.services.kpi_service_refactored import KpiCalculationServiceRefactored


class TestKpiServiceEnhancements:
    """Test cases for KpiService new methods."""
    
    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager."""
        mock_db = Mock()
        mock_db.get_connection.return_value.__enter__ = Mock()
        mock_db.get_connection.return_value.__exit__ = Mock()
        return mock_db
    
    @pytest.fixture
    def kpi_service(self, mock_db_manager):
        """Create KpiService with mocked dependencies."""
        with patch('src.services.kpi_service_refactored.get_db_manager', return_value=mock_db_manager):
            return KpiCalculationServiceRefactored()
    
    @pytest.fixture
    def sample_kpi_definition(self):
        """Sample KPI definition for testing."""
        return {
            'id': 'total_volume',
            'name': 'Volume Total',
            'description': 'Volume total de operações',
            'query': 'SELECT SUM(valor) as value FROM operacoes WHERE data >= %s AND data <= %s',
            'format': 'currency',
            'category': 'volume',
            'icon': 'TrendingUp',
            'parameters': ['start_date', 'end_date']
        }
    
    def test_calculate_single_kpi_success(self, kpi_service, sample_kpi_definition, mock_db_manager):
        """Test successful single KPI calculation."""
        # Mock database connection and query result
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_db_manager.get_connection.return_value.__enter__.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_cursor.fetchone.return_value = (Decimal('1500000.00'),)
        
        # Mock _calculate_real_kpi_value
        with patch.object(kpi_service, '_calculate_real_kpi_value', return_value=Decimal('1500000.00')):
            result = kpi_service.calculate_single_kpi(sample_kpi_definition, 'L2M')
        
        assert result is not None
        assert result == Decimal('1500000.00')
    
    def test_calculate_single_kpi_none_result(self, kpi_service, sample_kpi_definition):
        """Test single KPI calculation with None result."""
        # Mock _calculate_real_kpi_value to return None
        with patch.object(kpi_service, '_calculate_real_kpi_value', return_value=None):
            result = kpi_service.calculate_single_kpi(sample_kpi_definition, 'L2M')
        
        assert result is None
    
    def test_calculate_single_kpi_error(self, kpi_service, sample_kpi_definition):
        """Test single KPI calculation with error."""
        # Mock _calculate_real_kpi_value to raise exception
        with patch.object(kpi_service, '_calculate_real_kpi_value', side_effect=Exception("Database error")):
            result = kpi_service.calculate_single_kpi(sample_kpi_definition, 'L2M')
        
        assert result is None
    
    def test_get_dashboard_kpis_success(self, kpi_service):
        """Test successful dashboard KPIs retrieval."""
        # Mock get_kpi_definitions
        mock_kpi_definitions = [
            {
                'id': 'total_volume',
                'name': 'Volume Total',
                'query': 'SELECT SUM(valor) as value FROM operacoes',
                'format': 'currency',
                'category': 'volume',
                'icon': 'TrendingUp'
            },
            {
                'id': 'average_spread',
                'name': 'Spread Médio',
                'query': 'SELECT AVG(spread) as value FROM operacoes',
                'format': 'percentage',
                'category': 'spread',
                'icon': 'DollarSign'
            }
        ]
        
        with patch.object(kpi_service, 'get_kpi_definitions', return_value=mock_kpi_definitions), \
             patch.object(kpi_service, 'calculate_single_kpi') as mock_calculate:
            
            # Mock calculate_single_kpi to return different values
            mock_calculate.side_effect = [Decimal('1500000.00'), Decimal('0.025')]
            
            result = kpi_service.get_dashboard_kpis('cambio', 'L2M')
        
        assert len(result) == 2
        assert result[0]['id'] == 'total_volume'
        assert result[0]['value'] == Decimal('1500000.00')
        assert result[1]['id'] == 'average_spread'
        assert result[1]['value'] == Decimal('0.025')
    
    def test_get_dashboard_kpis_with_none_values(self, kpi_service):
        """Test dashboard KPIs retrieval with some None values."""
        mock_kpi_definitions = [
            {
                'id': 'total_volume',
                'name': 'Volume Total',
                'query': 'SELECT SUM(valor) as value FROM operacoes',
                'format': 'currency',
                'category': 'volume',
                'icon': 'TrendingUp'
            },
            {
                'id': 'average_spread',
                'name': 'Spread Médio',
                'query': 'SELECT AVG(spread) as value FROM operacoes',
                'format': 'percentage',
                'category': 'spread',
                'icon': 'DollarSign'
            }
        ]
        
        with patch.object(kpi_service, 'get_kpi_definitions', return_value=mock_kpi_definitions), \
             patch.object(kpi_service, 'calculate_single_kpi') as mock_calculate:
            
            # Mock calculate_single_kpi to return one None value
            mock_calculate.side_effect = [Decimal('1500000.00'), None]
            
            result = kpi_service.get_dashboard_kpis('cambio', 'L2M')
        
        assert len(result) == 2
        assert result[0]['value'] == Decimal('1500000.00')
        assert result[1]['value'] is None
    
    def test_get_dashboard_kpis_empty_definitions(self, kpi_service):
        """Test dashboard KPIs retrieval with empty definitions."""
        with patch.object(kpi_service, 'get_kpi_definitions', return_value=[]):
            result = kpi_service.get_dashboard_kpis('cambio', 'L2M')
        
        assert result == []
    
    def test_get_dashboard_kpis_error(self, kpi_service):
        """Test dashboard KPIs retrieval with error."""
        with patch.object(kpi_service, 'get_kpi_definitions', side_effect=Exception("Config error")):
            result = kpi_service.get_dashboard_kpis('cambio', 'L2M')
        
        assert result == []
    
    def test_calculate_kpi_value_from_dict_success(self, kpi_service, sample_kpi_definition):
        """Test calculate_kpi_value_from_dict returns Optional correctly."""
        # Mock _calculate_real_kpi_value
        with patch.object(kpi_service, '_calculate_real_kpi_value', return_value=Decimal('1500000.00')):
            result = kpi_service.calculate_kpi_value_from_dict(sample_kpi_definition, 'L2M')
        
        assert result is not None
        assert result == Decimal('1500000.00')
    
    def test_calculate_kpi_value_from_dict_none(self, kpi_service, sample_kpi_definition):
        """Test calculate_kpi_value_from_dict returns None correctly."""
        # Mock _calculate_real_kpi_value to return None
        with patch.object(kpi_service, '_calculate_real_kpi_value', return_value=None):
            result = kpi_service.calculate_kpi_value_from_dict(sample_kpi_definition, 'L2M')
        
        assert result is None
    
    def test_calculate_kpi_value_from_dict_error(self, kpi_service, sample_kpi_definition):
        """Test calculate_kpi_value_from_dict with error returns None."""
        # Mock _calculate_real_kpi_value to raise exception
        with patch.object(kpi_service, '_calculate_real_kpi_value', side_effect=Exception("Database error")):
            result = kpi_service.calculate_kpi_value_from_dict(sample_kpi_definition, 'L2M')
        
        assert result is None
    
    def test_integration_with_snapshot_service(self, kpi_service):
        """Test integration with SnapshotService workflow."""
        # Mock the complete workflow as used by SnapshotService
        mock_kpi_definitions = [
            {
                'id': 'total_volume',
                'name': 'Volume Total',
                'query': 'SELECT SUM(valor) as value FROM operacoes',
                'format': 'currency',
                'category': 'volume',
                'icon': 'TrendingUp'
            }
        ]
        
        with patch.object(kpi_service, 'get_kpi_definitions', return_value=mock_kpi_definitions), \
             patch.object(kpi_service, 'calculate_single_kpi', return_value=Decimal('1500000.00')):
            
            # Simulate SnapshotService workflow
            dashboard_kpis = kpi_service.get_dashboard_kpis('cambio', 'L2M')
            
            # Verify the structure matches what SnapshotService expects
            assert len(dashboard_kpis) == 1
            kpi = dashboard_kpis[0]
            assert 'id' in kpi
            assert 'name' in kpi
            assert 'value' in kpi
            assert 'format' in kpi
            assert 'category' in kpi
            assert 'icon' in kpi
    
    def test_performance_with_multiple_kpis(self, kpi_service):
        """Test performance with multiple KPIs."""
        # Create multiple KPI definitions
        mock_kpi_definitions = []
        for i in range(10):
            mock_kpi_definitions.append({
                'id': f'kpi_{i}',
                'name': f'KPI {i}',
                'query': f'SELECT {i} as value',
                'format': 'number',
                'category': 'test',
                'icon': 'BarChart'
            })
        
        with patch.object(kpi_service, 'get_kpi_definitions', return_value=mock_kpi_definitions), \
             patch.object(kpi_service, 'calculate_single_kpi', return_value=Decimal('100.00')):
            
            start_time = datetime.now()
            result = kpi_service.get_dashboard_kpis('cambio', 'L2M')
            end_time = datetime.now()
            
            # Verify all KPIs were processed
            assert len(result) == 10
            
            # Verify reasonable performance (should be fast with mocked data)
            processing_time = (end_time - start_time).total_seconds()
            assert processing_time < 1.0  # Should complete in less than 1 second
    
    def test_error_handling_resilience(self, kpi_service):
        """Test error handling resilience with mixed success/failure."""
        mock_kpi_definitions = [
            {'id': 'success_kpi', 'name': 'Success KPI', 'query': 'SELECT 1'},
            {'id': 'error_kpi', 'name': 'Error KPI', 'query': 'SELECT 1'},
            {'id': 'null_kpi', 'name': 'Null KPI', 'query': 'SELECT 1'}
        ]
        
        def mock_calculate_side_effect(kpi_def, client_id):
            if kpi_def['id'] == 'success_kpi':
                return Decimal('100.00')
            elif kpi_def['id'] == 'error_kpi':
                raise Exception("Database error")
            else:  # null_kpi
                return None
        
        with patch.object(kpi_service, 'get_kpi_definitions', return_value=mock_kpi_definitions), \
             patch.object(kpi_service, 'calculate_single_kpi', side_effect=mock_calculate_side_effect):
            
            result = kpi_service.get_dashboard_kpis('cambio', 'L2M')
        
        # Should return all KPIs, even with errors
        assert len(result) == 3
        assert result[0]['value'] == Decimal('100.00')  # success_kpi
        assert result[1]['value'] is None  # error_kpi (error handled)
        assert result[2]['value'] is None  # null_kpi
    
    def test_client_id_parameter_handling(self, kpi_service, sample_kpi_definition):
        """Test proper client_id parameter handling."""
        with patch.object(kpi_service, '_calculate_real_kpi_value') as mock_calculate:
            mock_calculate.return_value = Decimal('1500000.00')
            
            # Test with different client IDs
            result1 = kpi_service.calculate_single_kpi(sample_kpi_definition, 'L2M')
            result2 = kpi_service.calculate_single_kpi(sample_kpi_definition, 'CLIENT2')
            
            # Verify client_id was passed correctly
            assert mock_calculate.call_count == 2
            call_args = mock_calculate.call_args_list
            assert call_args[0][0][1] == 'L2M'  # Second argument is client_id
            assert call_args[1][0][1] == 'CLIENT2' 