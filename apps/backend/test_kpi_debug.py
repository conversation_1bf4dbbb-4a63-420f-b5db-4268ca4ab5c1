#!/usr/bin/env python
"""Debug KPI calculation issue."""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

load_dotenv()

from src.services.kpi_service_refactored import get_kpi_service_refactored

# Initialize service
service = get_kpi_service_refactored()

# Test calculate real KPI value
print("🔍 Testing KPI calculation...")

# Test total_volume
kpi_id = "total_volume"
print(f"\n🔧 Testing direct SQL query...")

# Test direct SQL
from src.tools.db_utils import load_db_config, build_connection_string, get_engine
from sqlalchemy import text

db_config = load_db_config(setor="cambio", cliente="L2M")
connection_string = build_connection_string(db_config)
engine = get_engine(connection_string)

with engine.connect() as conn:
    result = conn.execute(text('''
        SELECT COALESCE(SUM(valor_me), 0) as volume_total
        FROM boleta
        WHERE valor_me IS NOT NULL
        AND valor_me > 0
    '''))
    row = result.fetchone()
    direct_value = float(row[0]) if row and row[0] else 0
    print(f"✅ Direct SQL result: {direct_value:,.2f}")

print(f"\n🔧 Testing service method...")
value = service._calculate_real_kpi_value(kpi_id, "L2M")
print(f"📊 Service result for {kpi_id}: {value}")

# Test with a simple dict
kpi_dict = {
    'id': 'total_volume',
    'name': 'Volume Total Negociado',
    'description': 'Test',
    'format_type': 'currency',
    'category': 'operational',
    'is_priority': True,
    'display_order': 0
}

result = service.calculate_kpi_value_from_dict(kpi_dict, "L2M")
print(f"\n📋 Full KPI result:")
print(f"   ID: {result['id']}")
print(f"   Value: {result['currentValue']}")
print(f"   Format: {result['format']}")