#!/usr/bin/env python3
"""
Teste para verificar se o sistema está escrevendo e lendo no cache Redis local.
"""

import os
import sys
import time
import json
import redis
from datetime import datetime
from pathlib import Path

# Adicionar o diretório raiz ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Carregar variáveis de ambiente
from dotenv import load_dotenv
load_dotenv()

# Importar os módulos de cache
from src.caching.redis_cache_manager import RedisCacheManager, get_redis_manager
from src.caching.unified_cache_system import get_unified_cache


def test_redis_connection():
    """Testa a conexão básica com Redis."""
    print("🔌 Testando conexão básica com Redis...")
    
    try:
        # Tentar conectar diretamente
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        client = redis.from_url(redis_url, decode_responses=True)
        
        # Testar ping
        response = client.ping()
        print(f"✅ Redis ping: {response}")
        
        # Testar operações básicas
        test_key = "test:connection"
        test_value = {"timestamp": datetime.now().isoformat(), "test": True}
        
        # Escrever
        client.setex(test_key, 60, json.dumps(test_value))
        print(f"✅ Escrita no Redis: {test_key}")
        
        # Ler
        stored_value = client.get(test_key)
        if stored_value:
            parsed_value = json.loads(stored_value)
            print(f"✅ Leitura do Redis: {parsed_value}")
        else:
            print("❌ Falha na leitura do Redis")
            
        # Limpar
        client.delete(test_key)
        print(f"✅ Chave removida: {test_key}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na conexão com Redis: {e}")
        return False


def test_redis_cache_manager():
    """Testa o RedisCacheManager."""
    print("\n📦 Testando RedisCacheManager...")
    
    try:
        # Obter instância do manager
        manager = get_redis_manager()
        
        if not manager.is_connected():
            print("❌ RedisCacheManager não está conectado")
            return False
            
        print(f"✅ RedisCacheManager conectado")
        
        # Testar operações
        test_key = "test:manager"
        test_data = {
            "kpi_id": "total_sales",
            "value": 12345.67,
            "timestamp": datetime.now().isoformat(),
            "metadata": {"currency": "BRL", "timeframe": "week"}
        }
        
        # Escrever
        success = manager.set(test_key, test_data, ttl=300)
        print(f"✅ Escrita via manager: {success}")
        
        # Ler
        cached_data = manager.get(test_key)
        if cached_data:
            print(f"✅ Leitura via manager: {cached_data['kpi_id']} = {cached_data['value']}")
        else:
            print("❌ Falha na leitura via manager")
            
        # Testar cache miss
        miss_data = manager.get("test:nonexistent")
        print(f"✅ Cache miss (esperado): {miss_data is None}")
        
        # Estatísticas
        stats = manager.get_stats()
        print(f"📊 Estatísticas: {stats}")
        
        # Limpar
        manager.delete(test_key)
        print(f"✅ Chave removida via manager")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no RedisCacheManager: {e}")
        return False


def test_unified_cache_system():
    """Testa o sistema de cache unificado."""
    print("\n🔄 Testando Sistema de Cache Unificado...")
    
    try:
        # Obter instância do cache unificado
        cache = get_unified_cache()
        
        # Limpar cache
        cache.clear()
        print("✅ Cache limpo")
        
        # Testar operações básicas
        namespace = "kpi:test"
        test_value = 98765.43
        
        # Escrever
        cache.set(namespace, test_value, kpi_id="test_kpi", timeframe="month", ttl=300)
        print(f"✅ Valor armazenado no cache unificado: {test_value}")
        
        # Ler (hit)
        cached_value = cache.get(namespace, kpi_id="test_kpi", timeframe="month")
        print(f"✅ Valor lido do cache: {cached_value}")
        
        # Ler (miss - timeframe diferente)
        miss_value = cache.get(namespace, kpi_id="test_kpi", timeframe="week")
        print(f"✅ Cache miss (esperado): {miss_value is None}")
        
        # Estatísticas
        stats = cache.get_stats()
        print(f"📊 Estatísticas do cache unificado:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
            
        return True
        
    except Exception as e:
        print(f"❌ Erro no sistema de cache unificado: {e}")
        return False


def test_performance():
    """Testa a performance do cache."""
    print("\n⚡ Testando Performance do Cache...")
    
    try:
        manager = get_redis_manager()
        if not manager.is_connected():
            print("❌ Redis não conectado para teste de performance")
            return False
            
        # Teste de escrita em lote
        print("📝 Testando escritas em lote...")
        start_time = time.time()
        
        for i in range(100):
            key = f"perf:test:{i}"
            data = {
                "id": i,
                "value": i * 10.5,
                "timestamp": datetime.now().isoformat()
            }
            manager.set(key, data, ttl=60)
            
        write_time = (time.time() - start_time) * 1000
        print(f"✅ 100 escritas em {write_time:.2f}ms ({write_time/100:.2f}ms por operação)")
        
        # Teste de leitura em lote
        print("📖 Testando leituras em lote...")
        start_time = time.time()
        hits = 0
        
        for i in range(100):
            key = f"perf:test:{i}"
            data = manager.get(key)
            if data:
                hits += 1
                
        read_time = (time.time() - start_time) * 1000
        print(f"✅ 100 leituras em {read_time:.2f}ms ({read_time/100:.2f}ms por operação)")
        print(f"✅ Cache hits: {hits}/100")
        
        # Limpeza
        for i in range(100):
            manager.delete(f"perf:test:{i}")
        print("✅ Dados de teste removidos")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de performance: {e}")
        return False


def main():
    """Executa todos os testes."""
    print("🧪 TESTE DO SISTEMA DE CACHE REDIS")
    print("=" * 50)
    
    # Verificar variáveis de ambiente
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    print(f"🔧 Redis URL: {redis_url}")
    
    # Executar testes
    tests = [
        ("Conexão Redis", test_redis_connection),
        ("RedisCacheManager", test_redis_cache_manager),
        ("Sistema Cache Unificado", test_unified_cache_system),
        ("Performance", test_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erro inesperado em {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumo
    print(f"\n{'='*50}")
    print("📋 RESUMO DOS TESTES")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Resultado: {passed}/{len(results)} testes passaram")
    
    if passed == len(results):
        print("🎉 Todos os testes passaram! O sistema de cache Redis está funcionando corretamente.")
    else:
        print("⚠️  Alguns testes falharam. Verifique a configuração do Redis.")


if __name__ == "__main__":
    main()
