"""
Enhanced <PERSON>rro<PERSON> for DataHero4 Pipeline
============================================

Robust error handling system with recovery strategies, structured logging,
and context propagation between pipeline nodes.

Features:
- Automatic error recovery with configurable strategies
- Structured error logging with context preservation
- Error categorization and severity assessment
- Graceful degradation patterns
- Circuit breaker for repeated failures
"""

import logging
import traceback
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable, Union
from functools import wraps
from contextlib import contextmanager

from ..models.pipeline_models import ErrorContext, ErrorSeverity, EnhancedPipelineState


logger = logging.getLogger(__name__)


class ErrorRecoveryStrategy:
    """Base class for error recovery strategies."""
    
    def can_recover(self, error: ErrorContext) -> bool:
        """Check if this strategy can recover from the error."""
        raise NotImplementedError
    
    def recover(self, error: ErrorContext, state: EnhancedPipelineState) -> EnhancedPipelineState:
        """Attempt to recover from the error."""
        raise NotImplementedError


class RetryStrategy(ErrorRecoveryStrategy):
    """Retry strategy with exponential backoff."""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    def can_recover(self, error: ErrorContext) -> bool:
        return error.recoverable and error.retry_count < self.max_retries
    
    def recover(self, error: ErrorContext, state: EnhancedPipelineState) -> EnhancedPipelineState:
        error.increment_retry()
        logger.info(f"Retrying operation (attempt {error.retry_count}/{self.max_retries})")
        return state





class GracefulDegradationStrategy(ErrorRecoveryStrategy):
    """Graceful degradation with reduced functionality."""
    
    def can_recover(self, error: ErrorContext) -> bool:
        return error.severity != ErrorSeverity.CRITICAL
    
    def recover(self, error: ErrorContext, state: EnhancedPipelineState) -> EnhancedPipelineState:
        logger.warning(f"Graceful degradation for error: {error.error_type}")
        
        # Mark state as degraded
        state.legacy_state["degraded_mode"] = True
        state.legacy_state["degradation_reason"] = error.error_message
        
        # Continue with reduced functionality
        return state


class CircuitBreaker:
    """Circuit breaker to prevent cascading failures."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half-open"
            else:
                raise Exception("Circuit breaker is open")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        
        time_since_failure = (datetime.now() - self.last_failure_time).seconds
        return time_since_failure >= self.recovery_timeout
    
    def _on_success(self):
        """Handle successful execution."""
        self.failure_count = 0
        self.state = "closed"
    
    def _on_failure(self):
        """Handle failed execution."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"


class EnhancedErrorHandler:
    """Enhanced error handler with multiple recovery strategies."""
    
    def __init__(self):
        self.recovery_strategies: List[ErrorRecoveryStrategy] = []
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.error_history: List[ErrorContext] = []
    
    def add_strategy(self, strategy: ErrorRecoveryStrategy) -> None:
        """Add a recovery strategy."""
        self.recovery_strategies.append(strategy)
    
    def get_circuit_breaker(self, operation: str) -> CircuitBreaker:
        """Get or create circuit breaker for operation."""
        if operation not in self.circuit_breakers:
            self.circuit_breakers[operation] = CircuitBreaker()
        return self.circuit_breakers[operation]
    
    def handle_error(
        self, 
        error: Exception, 
        state: EnhancedPipelineState,
        phase: str,
        node_name: Optional[str] = None,
        user_input: Optional[str] = None
    ) -> EnhancedPipelineState:
        """Handle an error with recovery strategies."""
        
        # Create error context
        error_context = self._create_error_context(
            error, phase, node_name, user_input
        )
        
        # Log structured error
        self._log_error(error_context, state)
        
        # Add error to state
        state.add_error(error_context)
        
        # Attempt recovery
        recovered_state = self._attempt_recovery(error_context, state)
        
        # Store in history
        self.error_history.append(error_context)
        
        return recovered_state
    
    def _create_error_context(
        self,
        error: Exception,
        phase: str,
        node_name: Optional[str] = None,
        user_input: Optional[str] = None
    ) -> ErrorContext:
        """Create structured error context."""
        
        # Determine error severity
        severity = self._assess_severity(error)
        
        # Determine if recoverable
        recoverable = self._is_recoverable(error, severity)
        
        return ErrorContext(
            error_id=str(uuid.uuid4()),
            error_type=type(error).__name__,
            error_message=str(error),
            severity=severity,
            phase=phase,
            node_name=node_name,
            exception_type=type(error).__name__,
            stack_trace=traceback.format_exc(),
            user_input=user_input,
            recoverable=recoverable,
            recovery_suggestions=self._generate_recovery_suggestions(error),
            affects_cache=self._affects_cache(error),
            affects_learning=self._affects_learning(error)
        )
    
    def _assess_severity(self, error: Exception) -> ErrorSeverity:
        """Assess error severity based on exception type."""
        critical_errors = [SystemExit, KeyboardInterrupt, MemoryError]
        high_errors = [ConnectionError, TimeoutError, PermissionError]
        medium_errors = [ValueError, TypeError, AttributeError]
        
        error_type = type(error)
        
        if any(isinstance(error, err_type) for err_type in critical_errors):
            return ErrorSeverity.CRITICAL
        elif any(isinstance(error, err_type) for err_type in high_errors):
            return ErrorSeverity.HIGH
        elif any(isinstance(error, err_type) for err_type in medium_errors):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _is_recoverable(self, error: Exception, severity: ErrorSeverity) -> bool:
        """Determine if error is recoverable."""
        if severity == ErrorSeverity.CRITICAL:
            return False
        
        # Network errors are usually recoverable
        if isinstance(error, (ConnectionError, TimeoutError)):
            return True
        
        # Validation errors might be recoverable with different input
        if isinstance(error, (ValueError, TypeError)):
            return True
        
        return True
    
    def _generate_recovery_suggestions(self, error: Exception) -> List[str]:
        """Generate recovery suggestions based on error type."""
        suggestions = []
        
        if isinstance(error, ConnectionError):
            suggestions.extend([
                "Check network connectivity",
                "Retry with exponential backoff",
                "Use cached data if available"
            ])
        elif isinstance(error, ValueError):
            suggestions.extend([
                "Validate input parameters",
                "Use default values",
                "Request user clarification"
            ])
        elif isinstance(error, TimeoutError):
            suggestions.extend([
                "Increase timeout duration",
                "Retry operation",
                "Use cached results"
            ])
        
        return suggestions
    
    def _affects_cache(self, error: Exception) -> bool:
        """Check if error affects cache system."""
        cache_related_errors = [ConnectionError, PermissionError]
        return any(isinstance(error, err_type) for err_type in cache_related_errors)
    
    def _affects_learning(self, error: Exception) -> bool:
        """Check if error affects learning system."""
        learning_related_errors = [ValueError, TypeError]
        return any(isinstance(error, err_type) for err_type in learning_related_errors)
    
    def _log_error(self, error_context: ErrorContext, state: EnhancedPipelineState) -> None:
        """Log structured error information."""
        logger.error(
            f"Pipeline error in {error_context.phase}",
            extra={
                "error_id": error_context.error_id,
                "error_type": error_context.error_type,
                "severity": error_context.severity.value,
                "phase": error_context.phase,
                "node_name": error_context.node_name,
                "recoverable": error_context.recoverable,
                "question": state.question,
                "client_id": state.client_id,
                "sector": state.sector
            }
        )
    
    def _attempt_recovery(
        self, 
        error_context: ErrorContext, 
        state: EnhancedPipelineState
    ) -> EnhancedPipelineState:
        """Attempt recovery using available strategies."""
        
        for strategy in self.recovery_strategies:
            if strategy.can_recover(error_context):
                try:
                    logger.info(f"Attempting recovery with {type(strategy).__name__}")
                    recovered_state = strategy.recover(error_context, state)
                    logger.info("Recovery successful")
                    return recovered_state
                except Exception as recovery_error:
                    logger.error(f"Recovery strategy failed: {recovery_error}")
                    continue
        
        # No recovery possible
        logger.error("All recovery strategies failed")
        error_context.recoverable = False
        return state


# Global error handler instance
_error_handler = EnhancedErrorHandler()

# Add default strategies
_error_handler.add_strategy(RetryStrategy(max_retries=3))
_error_handler.add_strategy(GracefulDegradationStrategy())


def handle_pipeline_error(
    phase: str,
    node_name: Optional[str] = None
):
    """Decorator for handling pipeline errors."""
    def decorator(func):
        @wraps(func)
        def wrapper(state: Union[EnhancedPipelineState, Dict[str, Any]], *args, **kwargs):
            original_is_dict = isinstance(state, dict)

            try:
                # Convert legacy state if needed
                if original_is_dict:
                    enhanced_state = _convert_legacy_state(state)
                else:
                    enhanced_state = state

                # Execute function with circuit breaker
                circuit_breaker = _error_handler.get_circuit_breaker(f"{phase}_{node_name or 'unknown'}")
                result = circuit_breaker.call(func, enhanced_state, *args, **kwargs)

                # Convert back to legacy format if needed
                if original_is_dict and isinstance(result, EnhancedPipelineState):
                    return _convert_to_legacy_state(result)
                elif original_is_dict and isinstance(result, dict):
                    # Function returned dict directly, merge with original state
                    state.update(result)
                    return state
                else:
                    return result

            except Exception as e:
                # Handle error
                user_input = enhanced_state.question if hasattr(enhanced_state, 'question') else None
                recovered_state = _error_handler.handle_error(
                    e, enhanced_state, phase, node_name, user_input
                )

                # Convert back to legacy format if needed
                if original_is_dict:
                    return _convert_to_legacy_state(recovered_state)
                else:
                    return recovered_state

        return wrapper
    return decorator


def _convert_legacy_state(legacy_state: Dict[str, Any]) -> EnhancedPipelineState:
    """Convert legacy state dict to enhanced state."""
    return EnhancedPipelineState(
        question=legacy_state.get("question", ""),
        client_id=legacy_state.get("client_id", ""),
        sector=legacy_state.get("sector", ""),
        channel=legacy_state.get("channel", "api"),
        legacy_state=legacy_state
    )


def _convert_to_legacy_state(enhanced_state: EnhancedPipelineState) -> Dict[str, Any]:
    """Convert enhanced state back to legacy dict."""
    result = enhanced_state.legacy_state.copy()
    result.update({
        "question": enhanced_state.question,
        "client_id": enhanced_state.client_id,
        "sector": enhanced_state.sector,
        "channel": enhanced_state.channel
    })
    
    # Add error information
    if enhanced_state.has_errors():
        latest_error = enhanced_state.get_latest_error()
        result["error"] = latest_error.error_message
        result["error_context"] = latest_error.model_dump()
    
    return result


@contextmanager
def error_context(phase: str, node_name: Optional[str] = None):
    """Context manager for error handling."""
    try:
        yield
    except Exception as e:
        logger.error(f"Error in {phase}/{node_name}: {e}")
        raise
