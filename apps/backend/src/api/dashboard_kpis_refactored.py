"""
Dashboard KPIs API - Versão Refatorada
=====================================

API endpoints para KPIs do dashboard usando o sistema de cache unificado.
"""

from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, Query, HTTPException
import logging
import time

from src.services.kpi_service_refactored import get_kpi_service_refactored

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])


@router.get("/kpis")
async def get_dashboard_kpis(
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query("L2M", description="Client identifier"),
    timeframe: str = Query("week", description="Time frame: 1d, week, month, quarter"),
    category: Optional[str] = Query(None, description="Optional category filter"),
    priority_only: bool = Query(True, description="Return only priority KPIs"),
    currency: str = Query("all", description="Currency filter: all, usd, eur, gbp"),
    force_refresh: bool = Query(False, description="Force cache refresh")
) -> Dict[str, Any]:
    """
    Obtém KPIs do dashboard com suporte a filtros e cache inteligente.
    
    Features:
    - Cache com TTL dinâmico baseado em volatilidade
    - Invalidação seletiva com force_refresh
    - Filtros por timeframe e currency
    - Métricas de performance
    """
    start_time = time.time()
    
    try:
        # Validar parâmetros
        valid_timeframes = ['1d', 'week', 'month', 'quarter']
        if timeframe not in valid_timeframes:
            raise HTTPException(400, f"Invalid timeframe. Must be one of: {valid_timeframes}")
        
        valid_currencies = ['all', 'usd', 'eur', 'gbp']
        if currency not in valid_currencies:
            raise HTTPException(400, f"Invalid currency. Must be one of: {valid_currencies}")
        
        # Log da requisição
        logger.info(
            f"📊 KPI request - client: {client_id}, timeframe: {timeframe}, "
            f"currency: {currency}, force_refresh: {force_refresh}"
        )
        
        # Obter serviço
        kpi_service = get_kpi_service_refactored()
        
        # Invalidar cache se force_refresh
        if force_refresh:
            logger.info("🔄 Force refresh requested - invalidating cache")
            
            # KPIs prioritários para invalidar
            priority_kpis = ['total_volume', 'average_spread', 'average_ticket', 
                           'conversion_rate', 'retention_rate']
            
            kpi_service.invalidate_kpis(
                kpi_ids=priority_kpis,
                client_id=client_id,
                timeframe=timeframe,
                currency=currency
            )
        
        # Obter KPIs
        kpis_list = kpi_service.get_dashboard_kpis(
            sector=sector,
            client_id=client_id,
            timeframe=timeframe,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        # Calcular tempo de processamento
        processing_time = round((time.time() - start_time) * 1000, 2)
        
        # Preparar resposta
        response = {
            "kpis": kpis_list,
            "total_count": len(kpis_list),
            "sector": sector,
            "client_id": client_id,
            "timeframe": timeframe,
            "currency": currency,
            "priority_only": priority_only,
            "generated_at": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "processing_time_ms": processing_time,
            "cache_refreshed": force_refresh
        }
        
        logger.info(
            f"✅ KPI API success - returned {len(kpis_list)} KPIs "
            f"in {processing_time}ms"
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error in KPI API: {e}", exc_info=True)
        raise HTTPException(500, "Internal server error")


@router.get("/kpis/cache-stats")
async def get_cache_statistics() -> Dict[str, Any]:
    """
    Retorna estatísticas do sistema de cache.
    
    Útil para monitoramento e otimização.
    """
    try:
        kpi_service = get_kpi_service_refactored()
        stats = kpi_service.get_cache_stats()
        
        return {
            "status": "success",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "cache_stats": stats
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting cache stats: {e}")
        raise HTTPException(500, "Error retrieving cache statistics")


@router.post("/kpis/invalidate-cache")
async def invalidate_kpi_cache(
    kpi_ids: Optional[list[str]] = Query(None, description="Specific KPI IDs to invalidate"),
    client_id: Optional[str] = Query(None, description="Specific client to invalidate"),
    timeframe: Optional[str] = Query(None, description="Specific timeframe to invalidate"),
    currency: Optional[str] = Query(None, description="Specific currency to invalidate")
) -> Dict[str, Any]:
    """
    Invalida cache de KPIs seletivamente.
    
    Permite invalidação granular por:
    - KPI IDs específicos
    - Cliente
    - Timeframe
    - Currency
    """
    try:
        kpi_service = get_kpi_service_refactored()
        
        # Log da invalidação
        logger.info(
            f"🗑️ Cache invalidation requested - "
            f"kpis: {kpi_ids}, client: {client_id}, "
            f"timeframe: {timeframe}, currency: {currency}"
        )
        
        # Executar invalidação
        kpi_service.invalidate_kpis(
            kpi_ids=kpi_ids,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        return {
            "status": "success",
            "message": "Cache invalidated successfully",
            "invalidated": {
                "kpi_ids": kpi_ids,
                "client_id": client_id,
                "timeframe": timeframe,
                "currency": currency
            },
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S")
        }
        
    except Exception as e:
        logger.error(f"❌ Error invalidating cache: {e}")
        raise HTTPException(500, "Error invalidating cache") 