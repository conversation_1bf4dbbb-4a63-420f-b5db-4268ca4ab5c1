"""
Dashboard KPIs API - Versão Otimizada 2025
==========================================

API otimizada seguindo melhores práticas de 2025:
- Async/await com SQLAlchemy 2.x
- Cache Redis com TTL inteligente
- Queries paralelas
- Stale-while-revalidate pattern
"""

from typing import Any, Dict, Optional, List
from fastapi import APIRouter, Depends, Query, HTTPException, BackgroundTasks
import logging
import time
import asyncio
import json
from datetime import datetime

import aioredis
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy import text

from src.services.kpi_service_optimized import get_optimized_kpi_service

logger = logging.getLogger(__name__)

# Router
router = APIRouter(prefix="/api/v2/dashboard", tags=["dashboard-v2"])

# Redis connection
redis_client = None

async def get_redis():
    """Get Redis client connection."""
    global redis_client
    if redis_client is None:
        redis_client = await aioredis.from_url(
            "redis://localhost:6379",
            encoding="utf-8",
            decode_responses=True
        )
    return redis_client


class DashboardOptimizer:
    """Otimizador de dashboard com cache e paralelização."""
    
    def __init__(self):
        self.kpi_service = get_optimized_kpi_service()
        self.cache_ttl_map = {
            '1d': 60,        # 1 minuto para dados diários
            'week': 300,     # 5 minutos para semanais
            'month': 900,    # 15 minutos para mensais
            'quarter': 1800  # 30 minutos para trimestrais
        }
    
    def _get_cache_key(
        self, 
        client_id: str, 
        timeframe: str, 
        currency: str,
        category: Optional[str] = None
    ) -> str:
        """Gera chave de cache única para combinação de filtros."""
        parts = [f"dashboard:v2:{client_id}:{timeframe}:{currency}"]
        if category:
            parts.append(category)
        return ":".join(parts)
    
    def _get_ttl(self, timeframe: str) -> int:
        """Retorna TTL apropriado baseado no timeframe."""
        return self.cache_ttl_map.get(timeframe, 300)
    
    async def get_dashboard_data(
        self,
        client_id: str,
        timeframe: str,
        currency: str,
        category: Optional[str] = None,
        priority_only: bool = True,
        force_refresh: bool = False,
        background_tasks: BackgroundTasks = None
    ) -> Dict[str, Any]:
        """
        Obtém dados do dashboard com cache e otimizações.
        
        Implementa padrão stale-while-revalidate:
        1. Retorna cache se disponível (mesmo se próximo de expirar)
        2. Atualiza em background se necessário
        """
        start_time = time.time()
        cache_key = self._get_cache_key(client_id, timeframe, currency, category)
        redis = await get_redis()
        
        # 1. Verificar cache
        if not force_refresh:
            try:
                cached_data = await redis.get(cache_key)
                if cached_data:
                    data = json.loads(cached_data)
                    cache_age = time.time() - data.get('cached_at', 0)
                    ttl = self._get_ttl(timeframe)
                    
                    # Se cache tem menos de 80% do TTL, retornar direto
                    if cache_age < (ttl * 0.8):
                        logger.info(f"✅ Cache hit (fresh): {cache_key}")
                        data['cache_status'] = 'hit'
                        data['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
                        return data
                    
                    # Se cache está próximo de expirar, retornar mas atualizar em background
                    if background_tasks and cache_age < ttl:
                        logger.info(f"🔄 Cache hit (stale): {cache_key}, refreshing in background")
                        background_tasks.add_task(
                            self._refresh_cache_background,
                            client_id, timeframe, currency, category, priority_only
                        )
                        data['cache_status'] = 'stale-while-revalidate'
                        data['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
                        return data
                        
            except Exception as e:
                logger.error(f"❌ Redis error: {e}")
        
        # 2. Se não tem cache ou force_refresh, calcular
        logger.info(f"🔍 Cache miss or refresh: {cache_key}")
        
        # Executar cálculo otimizado
        kpis = await self._calculate_kpis_async(
            client_id, timeframe, currency, category, priority_only
        )
        
        # Preparar resposta
        response_data = {
            "kpis": kpis,
            "total_count": len(kpis),
            "client_id": client_id,
            "timeframe": timeframe,
            "currency": currency,
            "priority_only": priority_only,
            "generated_at": datetime.now().isoformat(),
            "cached_at": time.time(),
            "cache_status": "miss",
            "response_time_ms": round((time.time() - start_time) * 1000, 2)
        }
        
        # 3. Salvar no cache
        ttl = self._get_ttl(timeframe)
        await redis.set(cache_key, json.dumps(response_data), ex=ttl)
        logger.info(f"💾 Cached with TTL {ttl}s: {cache_key}")
        
        return response_data
    
    async def _calculate_kpis_async(
        self,
        client_id: str,
        timeframe: str,
        currency: str,
        category: Optional[str],
        priority_only: bool
    ) -> List[Dict[str, Any]]:
        """
        Calcula KPIs usando o serviço otimizado.
        Como o serviço usa ThreadPoolExecutor, wrappamos em run_in_executor.
        """
        loop = asyncio.get_event_loop()
        
        # Executar cálculo em thread pool para não bloquear event loop
        kpis = await loop.run_in_executor(
            None,
            self.kpi_service.get_dashboard_kpis_optimized,
            "cambio",  # sector
            client_id,
            timeframe,
            category,
            priority_only,
            currency
        )
        
        return kpis
    
    async def _refresh_cache_background(
        self,
        client_id: str,
        timeframe: str,
        currency: str,
        category: Optional[str],
        priority_only: bool
    ):
        """Atualiza cache em background."""
        try:
            logger.info(f"🔄 Background refresh starting for {client_id}/{timeframe}/{currency}")
            
            # Calcular novos dados
            kpis = await self._calculate_kpis_async(
                client_id, timeframe, currency, category, priority_only
            )
            
            # Salvar no cache
            cache_key = self._get_cache_key(client_id, timeframe, currency, category)
            redis = await get_redis()
            
            response_data = {
                "kpis": kpis,
                "total_count": len(kpis),
                "client_id": client_id,
                "timeframe": timeframe,
                "currency": currency,
                "priority_only": priority_only,
                "generated_at": datetime.now().isoformat(),
                "cached_at": time.time(),
                "cache_status": "refreshed"
            }
            
            ttl = self._get_ttl(timeframe)
            await redis.set(cache_key, json.dumps(response_data), ex=ttl)
            
            logger.info(f"✅ Background refresh completed for {cache_key}")
            
        except Exception as e:
            logger.error(f"❌ Background refresh failed: {e}")


# Instância global do otimizador
optimizer = DashboardOptimizer()


@router.get("/kpis")
async def get_dashboard_kpis_v2(
    background_tasks: BackgroundTasks,
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query("L2M", description="Client identifier"),
    timeframe: str = Query("week", description="Time frame: 1d, week, month, quarter"),
    category: Optional[str] = Query(None, description="Optional category filter"),
    priority_only: bool = Query(True, description="Return only priority KPIs"),
    currency: str = Query("all", description="Currency filter: all, usd, eur, gbp"),
    force_refresh: bool = Query(False, description="Force cache refresh")
) -> Dict[str, Any]:
    """
    API otimizada v2 para KPIs do dashboard.
    
    Features 2025:
    - Cache Redis com TTL inteligente
    - Stale-while-revalidate pattern
    - Queries paralelas
    - Response time < 800ms (P95)
    """
    try:
        # Validar parâmetros
        valid_timeframes = ['1d', 'week', 'month', 'quarter']
        if timeframe not in valid_timeframes:
            raise HTTPException(400, f"Invalid timeframe. Must be one of: {valid_timeframes}")
        
        valid_currencies = ['all', 'usd', 'eur', 'gbp']
        if currency not in valid_currencies:
            raise HTTPException(400, f"Invalid currency. Must be one of: {valid_currencies}")
        
        # Obter dados otimizados
        result = await optimizer.get_dashboard_data(
            client_id=client_id,
            timeframe=timeframe,
            currency=currency,
            category=category,
            priority_only=priority_only,
            force_refresh=force_refresh,
            background_tasks=background_tasks
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error in optimized KPI API: {e}", exc_info=True)
        raise HTTPException(500, "Internal server error")


@router.get("/kpis/batch")
async def get_dashboard_kpis_batch(
    background_tasks: BackgroundTasks,
    client_id: str = Query("L2M", description="Client identifier"),
    filters: str = Query(..., description="JSON array of filter combinations")
) -> Dict[str, Any]:
    """
    API para buscar múltiplas combinações de filtros em paralelo.
    
    Exemplo de filters:
    [{"timeframe": "week", "currency": "usd"}, {"timeframe": "month", "currency": "all"}]
    """
    try:
        filter_list = json.loads(filters)
        if not isinstance(filter_list, list):
            raise ValueError("Filters must be a JSON array")
        
        # Executar queries em paralelo
        tasks = []
        for filter_combo in filter_list:
            task = optimizer.get_dashboard_data(
                client_id=client_id,
                timeframe=filter_combo.get('timeframe', 'week'),
                currency=filter_combo.get('currency', 'all'),
                priority_only=True,
                background_tasks=background_tasks
            )
            tasks.append(task)
        
        # Aguardar todos os resultados
        results = await asyncio.gather(*tasks)
        
        return {
            "batch_results": results,
            "total_combinations": len(results),
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except json.JSONDecodeError:
        raise HTTPException(400, "Invalid JSON in filters parameter")
    except Exception as e:
        logger.error(f"❌ Error in batch API: {e}")
        raise HTTPException(500, "Internal server error")


@router.get("/kpis/warmup")
async def warmup_cache(
    background_tasks: BackgroundTasks,
    client_id: str = Query("L2M", description="Client identifier")
) -> Dict[str, Any]:
    """
    Pré-aquece o cache para combinações comuns de filtros.
    """
    common_combinations = [
        ("1d", "all"),
        ("week", "all"),
        ("week", "usd"),
        ("month", "all"),
    ]
    
    for timeframe, currency in common_combinations:
        background_tasks.add_task(
            optimizer._refresh_cache_background,
            client_id, timeframe, currency, None, True
        )
    
    return {
        "status": "warmup_scheduled",
        "combinations": len(common_combinations),
        "message": "Cache warmup initiated in background"
    } 