"""
Dashboard KPIs API - Versão Refatorada
=====================================

API endpoints para KPIs do dashboard usando o sistema de cache unificado.
"""

from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, Query, HTTPException
import logging
import time

from src.services.kpi_service_refactored import get_kpi_service_refactored
from src.services.kpi_service_railway_optimized import get_railway_optimized_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])


@router.get("/kpis")
async def get_dashboard_kpis(
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query("L2M", description="Client identifier"),
    timeframe: str = Query("week", description="Time frame: 1d, week, month, quarter"),
    category: Optional[str] = Query(None, description="Optional category filter"),
    priority_only: bool = Query(True, description="Return only priority KPIs"),
    currency: str = Query("all", description="Currency filter: all, usd, eur, gbp"),
    force_refresh: bool = Query(False, description="Force cache refresh")
) -> Dict[str, Any]:
    """
    Obtém KPIs do dashboard com suporte a filtros e cache inteligente.
    
    Features:
    - Cache com TTL dinâmico baseado em volatilidade
    - Invalidação seletiva com force_refresh
    - Filtros por timeframe e currency
    - Métricas de performance
    - Otimizado para Railway
    """
    start_time = time.time()
    
    try:
        # Validar parâmetros
        valid_timeframes = ['1d', 'week', 'month', 'quarter']
        if timeframe not in valid_timeframes:
            raise HTTPException(400, f"Invalid timeframe. Must be one of: {valid_timeframes}")
        
        valid_currencies = ['all', 'usd', 'eur', 'gbp']
        if currency not in valid_currencies:
            raise HTTPException(400, f"Invalid currency. Must be one of: {valid_currencies}")
        
        # Log da requisição
        logger.info(
            f"📊 KPI request - client: {client_id}, timeframe: {timeframe}, "
            f"currency: {currency}, force_refresh: {force_refresh}"
        )
        
        # Usar serviço otimizado para Railway
        optimized_service = get_railway_optimized_service()
        
        # Se force_refresh, limpar cache in-memory
        if force_refresh:
            logger.info("🔄 Force refresh requested")
            # O serviço Railway não tem método de invalidação específico,
            # mas o force_refresh fará o cálculo ignorar o cache
        
        # Obter KPIs usando método otimizado
        result = optimized_service.get_dashboard_kpis_fast(
            sector=sector,
            client_id=client_id,
            timeframe=timeframe,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        # Adicionar metadados da API
        result['api_version'] = 'v1-optimized'
        result['force_refresh'] = force_refresh
        
        # Log de sucesso
        logger.info(
            f"✅ KPI API success - returned {result['total_count']} KPIs "
            f"in {result.get('processing_time_ms', 'N/A')}ms"
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error in KPI API: {e}", exc_info=True)
        raise HTTPException(500, "Internal server error")


@router.get("/kpis/cache-stats")
async def get_cache_statistics() -> Dict[str, Any]:
    """
    Retorna estatísticas do sistema de cache.
    
    Útil para monitoramento e otimização.
    """
    try:
        kpi_service = get_kpi_service_refactored()
        stats = kpi_service.get_cache_stats()
        
        return {
            "status": "success",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "cache_stats": stats
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting cache stats: {e}")
        raise HTTPException(500, "Error retrieving cache statistics")


@router.post("/kpis/invalidate-cache")
async def invalidate_kpi_cache(
    kpi_ids: Optional[list[str]] = Query(None, description="Specific KPI IDs to invalidate"),
    client_id: Optional[str] = Query(None, description="Specific client to invalidate"),
    timeframe: Optional[str] = Query(None, description="Specific timeframe to invalidate"),
    currency: Optional[str] = Query(None, description="Specific currency to invalidate")
) -> Dict[str, Any]:
    """
    Invalida cache de KPIs seletivamente.
    
    Permite invalidação granular por:
    - KPI IDs específicos
    - Cliente
    - Timeframe
    - Currency
    """
    try:
        kpi_service = get_kpi_service_refactored()
        
        # Log da invalidação
        logger.info(
            f"🗑️ Cache invalidation requested - "
            f"kpis: {kpi_ids}, client: {client_id}, "
            f"timeframe: {timeframe}, currency: {currency}"
        )
        
        # Executar invalidação
        kpi_service.invalidate_kpis(
            kpi_ids=kpi_ids,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        return {
            "status": "success",
            "message": "Cache invalidated successfully",
            "invalidated": {
                "kpi_ids": kpi_ids,
                "client_id": client_id,
                "timeframe": timeframe,
                "currency": currency
            },
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S")
        }
        
    except Exception as e:
        logger.error(f"❌ Error invalidating cache: {e}")
        raise HTTPException(500, "Error invalidating cache")