"""
KPI Service - DataHero4
=======================

Service layer for KPI calculations and data formatting.
Handles KPI retrieval, calculation, and formatting for dashboard consumption.
"""

import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import random

from sqlalchemy.orm import Session
from sqlalchemy import text

from src.models.learning_models import KpiDefinition
from src.utils.learning_db_utils import get_db_manager
from src.caching.hierarchical_cache import get_hierarchical_cache
from src.services.kpi_query_manager_json import KpiQueryManagerJSON

logger = logging.getLogger(__name__)


class KpiCalculationService:
    """Service for calculating and formatting KPI data."""

    def __init__(self):
        self.cache = get_hierarchical_cache()
        self.db_manager = None
        # Smart SaaS-ready cache with intelligent TTL and multi-level caching
        self._kpi_cache = {}
        self._cache_metadata = {}  # Track cache creation time and TTL
        self._cache_ttl = 300  # 5 minutes default cache TTL
        self._init_db_connection()
        # Initialize KPI Query Manager with JSON backend
        self.query_manager = KpiQueryManagerJSON(sector="cambio")
    
    def _init_db_connection(self):
        """Initialize database connection."""
        try:
            self.db_manager = get_db_manager()
            logger.info("✅ KPI Service: Using learning database manager")
        except Exception as e:
            logger.warning(f"Learning DB not available, using fallback: {e}")
            try:
                # Use fallback connection for KPI data
                from src.utils.learning_db_utils import LearningDBManager
                import os

                # Load environment variables
                from dotenv import load_dotenv
                load_dotenv()

                self.db_manager = LearningDBManager(
                    host=os.getenv('DB_CAMBIO_HOST'),
                    port=int(os.getenv('DB_CAMBIO_PORT', '5432')),
                    database=os.getenv('DB_CAMBIO_NAME'),
                    user=os.getenv('DB_CAMBIO_USER'),
                    password=os.getenv('DB_CAMBIO_PASSWORD')
                )
                logger.info("✅ KPI Service: Using fallback database manager")
            except Exception as fallback_error:
                logger.error(f"❌ KPI Service: Failed to initialize database connection: {fallback_error}")
                self.db_manager = None

    def _get_intelligent_ttl(self, timeframe: str) -> int:
        """
        Smart TTL based on timeframe - shorter periods get shorter cache duration.
        SaaS-optimized for cloud environments like Railway.
        """
        ttl_mapping = {
            '1d': 300,    # 5 minutes - most volatile data
            'week': 600,  # 10 minutes - moderate volatility  
            'month': 1800,  # 30 minutes - stable data
            'quarter': 3600  # 1 hour - very stable data
        }
        return ttl_mapping.get(timeframe, 600)  # Default: 10 minutes

    def _get_smart_cache_key(self, kpi_id: str, client_id: str, timeframe: str, currency: str, data_type: str = "VALUE") -> str:
        """Generate intelligent cache key with data type separation for SaaS."""
        return f"{data_type}:{client_id}:{kpi_id}:{timeframe}:{currency}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid based on intelligent TTL."""
        if cache_key not in self._cache_metadata:
            return False
        
        metadata = self._cache_metadata[cache_key]
        age = time.time() - metadata['created_at']
        return age < metadata['ttl']

    def _get_from_smart_cache(self, cache_key: str) -> Optional[Any]:
        """Smart cache retrieval with TTL validation."""
        if not self._is_cache_valid(cache_key):
            # Clean expired cache entry
            if cache_key in self._kpi_cache:
                del self._kpi_cache[cache_key]
            if cache_key in self._cache_metadata:
                del self._cache_metadata[cache_key]
            return None
        
        return self._kpi_cache.get(cache_key)

    def _set_smart_cache(self, cache_key: str, value: Any, ttl: int):
        """Smart cache storage with metadata tracking."""
        self._kpi_cache[cache_key] = value
        self._cache_metadata[cache_key] = {
            'created_at': time.time(),
            'ttl': ttl
        }

    def get_kpi_definitions(self,
                           sector: str = "cambio",
                           category: Optional[str] = None,
                           active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Get KPI definitions from JSON configuration file.

        Args:
            sector: Business sector filter
            category: KPI category filter (optional)
            active_only: Only return active KPIs

        Returns:
            List of KPI definitions as dictionaries
        """
        try:
            # Load KPI configuration from JSON file
            import json
            from pathlib import Path

            # Try multiple possible paths for Railway compatibility
            possible_paths = [
                Path(f"src/config/setores/{sector}/kpis-exchange-json.json"),  # Local development
                Path(f"apps/backend/src/config/setores/{sector}/kpis-exchange-json.json"),  # Railway
            ]

            config_path = None
            for path in possible_paths:
                if path.exists():
                    config_path = path
                    break

            if not config_path:
                logger.error(f"❌ KPI configuration file not found in any of: {[str(p) for p in possible_paths]}")
                return []

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            logger.info(f"📋 Loaded KPI configuration from {config_path}")

            # Extract KPIs from categories
            kpi_definitions = []
            display_order = 0

            for cat in config.get('categories', []):
                cat_id = cat['id']

                # Filter by category if specified
                if category and cat_id != category:
                    continue

                for kpi in cat.get('kpis', []):
                    # Map format type from unit
                    format_type = self._map_format_type(kpi.get('unit', ''))

                    # Determine chart type
                    chart_type = self._map_chart_type(cat_id, kpi['id'])

                    # Check if priority
                    is_priority = self._is_priority_kpi(kpi['id'])

                    kpi_def = {
                        'id': kpi['id'],
                        'name': kpi['name'],
                        'description': kpi['description'],
                        'category': cat_id,
                        'formula': kpi['formula'],
                        'unit': kpi.get('unit'),
                        'format_type': format_type,
                        'frequency': kpi.get('frequency'),
                        'importance': kpi.get('importance'),
                        'sector': sector,
                        'is_active': True,
                        'chart_type': chart_type,
                        'is_priority': is_priority,
                        'display_order': display_order,
                        'alert_config': self._get_alert_config(kpi['id'], format_type) if is_priority else None
                    }

                    kpi_definitions.append(kpi_def)
                    display_order += 1

            logger.info(f"📊 Found {len(kpi_definitions)} KPI definitions")
            return kpi_definitions

        except Exception as e:
            logger.error(f"Error loading KPI definitions from JSON: {e}")
            return []

    def _map_format_type(self, unit: str) -> str:
        """Map unit to format type for frontend compatibility."""
        if not unit:
            return 'number'

        unit_lower = unit.lower()
        if 'r$' in unit_lower or 'us$' in unit_lower or 'monetário' in unit_lower:
            return 'currency'
        elif '%' in unit_lower or 'percentual' in unit_lower:
            return 'percentage'
        else:
            return 'number'

    def _map_chart_type(self, category: str, kpi_id: str) -> str:
        """Determine appropriate chart type based on KPI category and ID."""
        # Priority KPIs get area charts for visual emphasis
        priority_kpis = ['total_volume', 'average_ticket', 'conversion_rate', 'retention_rate']
        if kpi_id in priority_kpis:
            return 'area'

        # Financial KPIs typically use line charts
        if category in ['financial', 'market']:
            return 'line'

        # Operational KPIs can use bar charts
        if category == 'operational':
            return 'bar'

        # Default to line
        return 'line'

    def _is_priority_kpi(self, kpi_id: str) -> bool:
        """Determine if KPI should be marked as priority."""
        priority_kpis = [
            'total_volume', 'average_ticket', 'conversion_rate',
            'retention_rate', 'average_spread', 'liquidity_index'
        ]
        return kpi_id in priority_kpis

    def _get_alert_config(self, kpi_id: str, format_type: str) -> Optional[Dict[str, Any]]:
        """Get alert configuration for priority KPIs."""
        if format_type == 'currency':
            return {
                "type": "above",
                "threshold": 3000000,
                "message": f"KPI {kpi_id} acima do limite operacional!"
            }
        elif format_type == 'percentage':
            return {
                "type": "below",
                "threshold": 90,
                "message": f"KPI {kpi_id} abaixo do esperado"
            }
        return None
    
    def _generate_real_chart_data(self, kpi_id: str, format_type: str, client_id: str = "L2M", timeframe: str = "week", currency: str = "all") -> List[Dict[str, Any]]:
        """Generate real chart data from client database for KPI visualization with filters."""
        # Check cache first with timeframe and currency filters
        cached_chart_data = self._get_cached_chart_data_with_filters(kpi_id, client_id, timeframe, currency)
        if cached_chart_data is not None:
            return cached_chart_data
            
        if not self.db_manager:
            logger.warning("Database manager not available, using fallback data")
            return self._generate_fallback_chart_data(kpi_id, format_type)

        try:
            # Import database tools and timeframe utils
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from src.utils.timeframe_utils import convert_to_interval
            from sqlalchemy import text

            # Connect to client database
            db_config = load_db_config(setor="cambio", cliente=client_id)
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)

            with engine.connect() as conn:
                chart_data = []
                
                # Get dynamic filters
                timeframe_sql = self._get_timeframe_sql(timeframe)
                currency_sql = self._get_currency_sql(currency)
                logger.info(f"🔍 Generating chart data with filters - Timeframe: {timeframe}, Currency: {currency}")

                if kpi_id == 'total_volume':
                    # Real volume data based on timeframe with filters
                    result = conn.execute(text(f'''
                        SELECT
                            TO_CHAR(DATE_TRUNC('day', data_criacao), 'DD/MM') as day,
                            COALESCE(SUM(valor_mn), 0) as volume
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND valor_mn IS NOT NULL
                        AND ({currency_sql})
                        GROUP BY DATE_TRUNC('day', data_criacao)
                        ORDER BY DATE_TRUNC('day', data_criacao)
                        LIMIT 10
                    '''))

                    for row in result:
                        chart_data.append({'name': row[0], 'value': float(row[1])})

                elif kpi_id == 'average_spread':
                    # Real spread data by month with filters
                    result = conn.execute(text(f'''
                        SELECT
                            TO_CHAR(DATE_TRUNC('month', data_taxa_cambio), 'Mon') as month,
                            ABS(AVG(taxa_cambio_venda - taxa_cambio_compra)) as spread
                        FROM taxa_cambio_oficial
                        WHERE ({timeframe_sql.replace('data_criacao', 'data_taxa_cambio')})
                        AND taxa_cambio_compra IS NOT NULL
                        AND taxa_cambio_venda IS NOT NULL
                        GROUP BY DATE_TRUNC('month', data_taxa_cambio)
                        ORDER BY DATE_TRUNC('month', data_taxa_cambio)
                        LIMIT 6
                    '''))

                    for row in result:
                        chart_data.append({'name': row[0], 'value': float(row[1])})

                elif kpi_id == 'average_ticket':
                    # Real average ticket by month with filters
                    result = conn.execute(text(f'''
                        SELECT
                            TO_CHAR(DATE_TRUNC('month', data_criacao), 'Mon') as month,
                            AVG(valor_mn) as ticket_medio
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND valor_mn IS NOT NULL
                        AND valor_mn > 0
                        AND ({currency_sql})
                        GROUP BY DATE_TRUNC('month', data_criacao)
                        ORDER BY DATE_TRUNC('month', data_criacao)
                        LIMIT 6
                    '''))

                    for row in result:
                        chart_data.append({'name': row[0], 'value': float(row[1])})

                elif kpi_id == 'conversion_rate':
                    # Real conversion rate by month with filters
                    result = conn.execute(text(f'''
                        SELECT
                            TO_CHAR(DATE_TRUNC('month', data_criacao), 'Mon') as month,
                            ROUND(
                                COUNT(CASE WHEN id_boleta_status IN (4, 7) THEN 1 END) * 100.0 /
                                NULLIF(COUNT(*), 0),
                                2
                            ) as conversion_rate
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND ({currency_sql})
                        GROUP BY DATE_TRUNC('month', data_criacao)
                        ORDER BY DATE_TRUNC('month', data_criacao)
                        LIMIT 6
                    '''))

                    for row in result:
                        chart_data.append({'name': row[0], 'value': float(row[1] or 0)})

                else:
                    # For other KPIs, generate based on volume data with filters
                    result = conn.execute(text(f'''
                        SELECT
                            TO_CHAR(DATE_TRUNC('month', data_criacao), 'Mon') as month,
                            COUNT(*) as operations
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND ({currency_sql})
                        GROUP BY DATE_TRUNC('month', data_criacao)
                        ORDER BY DATE_TRUNC('month', data_criacao)
                    '''))

                    for row in result:
                        # Scale based on format type
                        value = float(row[1])
                        if format_type == 'currency':
                            value *= 50000  # Scale to reasonable currency values
                        elif format_type == 'percentage':
                            value = min(95, max(70, value / 10))  # Convert to percentage

                        chart_data.append({'name': row[0], 'value': value})

                # Ensure we have at least some data
                if not chart_data:
                    chart_data = self._generate_fallback_chart_data(kpi_id, format_type)
                else:
                    # Cache the generated data with filters
                    self._set_cached_chart_data_with_filters(kpi_id, client_id, timeframe, currency, chart_data)

                logger.info(f"✅ Generated real chart data for {kpi_id} (timeframe: {timeframe}, currency: {currency}): {len(chart_data)} points")
                return chart_data

        except Exception as e:
            logger.error(f"❌ Error generating real chart data for {kpi_id}: {e}")
            return self._generate_fallback_chart_data(kpi_id, format_type)

    def _generate_fallback_chart_data(self, kpi_id: str, format_type: str) -> List[Dict[str, Any]]:
        """Generate fallback chart data when real data is not available."""
        months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun']
        data = []

        # Generate realistic fallback data based on KPI type
        base_values = {
            'total_volume': [220000000, 235000000, 210000000, 260000000, 285000000, 295000000],
            'average_spread': [0.000032, 0.000028, 0.000030, 0.000027, 0.000025, 0.000024],
            'conversion_rate': [82.5, 84.2, 83.8, 85.1, 86.3, 87.1],
            'retention_rate': [88.5, 89.2, 87.8, 90.1, 91.2, 92.0],
            'average_ticket': [950000, 980000, 920000, 1020000, 1050000, 1080000]
        }

        if kpi_id in base_values:
            values = base_values[kpi_id]
        else:
            # Generate values based on format type
            if format_type == 'currency':
                values = [random.randint(50000000, 300000000) for _ in months]
            elif format_type == 'percentage':
                values = [random.uniform(75, 95) for _ in months]
            else:
                values = [random.randint(500000, 1500000) for _ in months]

        for month, value in zip(months, values):
            data.append({'name': month, 'value': value})

        return data
    
    def _calculate_trend(self, chart_data: List[Dict[str, Any]]) -> str:
        """Calculate trend based on chart data."""
        if len(chart_data) < 2:
            return 'stable'
        
        first_value = chart_data[0]['value']
        last_value = chart_data[-1]['value']
        
        change_percent = ((last_value - first_value) / first_value) * 100
        
        if change_percent > 2:
            return 'up'
        elif change_percent < -2:
            return 'down'
        else:
            return 'stable'
    
    def _calculate_change_percent(self, chart_data: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate percentage change from previous period."""
        if len(chart_data) < 2:
            return None
        
        try:
            previous_value = chart_data[-2]['value']
            current_value = chart_data[-1]['value']
            
            # Validate values are numbers
            if not isinstance(previous_value, (int, float)) or not isinstance(current_value, (int, float)):
                logger.warning(f"⚠️ Invalid values for change calculation: prev={previous_value}, curr={current_value}")
                return None
            
            # Check for zero or very small denominators
            if abs(previous_value) < 1e-10:  # Practically zero
                logger.info(f"📊 Previous value too small for percentage calculation: {previous_value}")
                return None
            
            change_percent = ((current_value - previous_value) / previous_value) * 100
            
            # Validate result is reasonable
            if not isinstance(change_percent, (int, float)) or abs(change_percent) > 10000:  # > 100x change
                logger.warning(f"⚠️ Unreasonable change percentage: {change_percent}%")
                return None
                
            return change_percent
            
        except (ZeroDivisionError, TypeError, ValueError) as e:
            logger.warning(f"⚠️ Error calculating change percent: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Unexpected error in change calculation: {e}")
            return None

    def calculate_kpi_value_from_dict(self, kpi_dict: Dict[str, Any], client_id: str = "L2M", timeframe: str = "week", currency: str = "all") -> Optional[Dict[str, Any]]:
        """
        Calculate current value for a KPI from dictionary data using REAL client data.

        Args:
            kpi_dict: KPI definition as dictionary
            client_id: Client identifier for database connection
            timeframe: Timeframe for chart data generation
            currency: Currency filter for calculations

        Returns:
            Dictionary with calculated KPI data
        """
        kpi_id = kpi_dict.get('id', 'unknown')
        
        try:
            logger.info(f"🔢 Calculating real KPI value for: {kpi_id}")

            # Calculate real current value with filters (FAST single query)
            current_value = self._calculate_real_kpi_value(kpi_id, client_id, timeframe, currency)

            # Generate chart data for visualization with timeframe and currency
            chart_data = self._generate_real_chart_data(kpi_id, kpi_dict.get('format_type', 'number'), client_id, timeframe, currency)
            logger.info(f"🔍 DEBUG - KPI {kpi_id}: current_value={current_value}, chart_data_type={type(chart_data)}, chart_data={chart_data}")

            # Use calculated value or default to 0
            if current_value is None:
                current_value = 0

            # Safely calculate trend and change from chart data
            trend = 'stable'
            change_percent = None
            
            if chart_data:
                try:
                    trend = self._calculate_trend(chart_data)
                except Exception as e:
                    logger.warning(f"⚠️ Error calculating trend for {kpi_id}: {e}")
                    trend = 'stable'
                
                try:
                    change_percent = self._calculate_change_percent(chart_data)
                except Exception as e:
                    logger.warning(f"⚠️ Error calculating change percent for {kpi_id}: {e}")
                    change_percent = None

            # Format alert configuration
            alert = None
            if kpi_dict.get('alert_config'):
                try:
                    alert = {
                        'type': kpi_dict['alert_config'].get('type'),
                        'threshold': kpi_dict['alert_config'].get('threshold'),
                        'message': kpi_dict['alert_config'].get('message')
                    }
                except Exception as e:
                    logger.warning(f"⚠️ Error formatting alert config for {kpi_id}: {e}")
                    alert = None

            logger.info(f"✅ KPI {kpi_id} calculated: {current_value} ({kpi_dict.get('format_type', 'number')})")

            # Generate chart_type using internal method if not present
            chart_type = kpi_dict.get('chart_type') or self._map_chart_type(kpi_dict.get('category', ''), kpi_dict.get('id', ''))

            # Safely format change_percent
            formatted_change_percent = None
            if change_percent is not None:
                try:
                    formatted_change_percent = round(float(change_percent), 1)
                except (ValueError, TypeError) as e:
                    logger.warning(f"⚠️ Error formatting change percent for {kpi_id}: {e}")
                    formatted_change_percent = None

            return {
                'id': kpi_dict.get('id', ''),
                'title': kpi_dict.get('name', ''),
                'description': kpi_dict.get('description', ''),
                'currentValue': current_value,
                'format': kpi_dict.get('format_type', 'number'),
                'changePercent': formatted_change_percent,
                'trend': trend,
                'chartType': chart_type,
                'chartData': chart_data or [],
                'alert': alert,
                'isPriority': kpi_dict.get('is_priority', False),
                'order': kpi_dict.get('display_order', 999),
                'category': kpi_dict.get('category', 'general'),
                'unit': kpi_dict.get('unit', ''),
                'frequency': kpi_dict.get('frequency', 'daily')
            }

        except Exception as e:
            logger.error(f"❌ Error calculating KPI {kpi_id}: {e}")
            # Log the full traceback for debugging
            import traceback
            logger.error(f"❌ Full traceback for {kpi_id}: {traceback.format_exc()}")
            return None

    def _get_cached_chart_data(self, kpi_id: str, client_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached chart data if still valid."""
        cache_key = f"{client_id}:{kpi_id}:chart"
        if cache_key in self._kpi_cache:
            cached_data = self._kpi_cache[cache_key]
            if time.time() - cached_data['timestamp'] < self._cache_ttl:
                logger.info(f"🚀 Using cached chart data for {kpi_id}")
                return cached_data['value']
            else:
                # Remove expired cache
                del self._kpi_cache[cache_key]
        return None

    def _set_cached_chart_data(self, kpi_id: str, client_id: str, chart_data: List[Dict[str, Any]]):
        """Cache chart data with timestamp."""
        cache_key = f"{client_id}:{kpi_id}:chart"
        self._kpi_cache[cache_key] = {
            'value': chart_data,
            'timestamp': time.time()
        }
        logger.info(f"💾 Cached chart data for {kpi_id}: {len(chart_data)} points")

    def _get_cached_kpi_value(self, kpi_id: str, client_id: str) -> Optional[float]:
        """Get cached KPI value if still valid."""
        cache_key = f"{client_id}:{kpi_id}"
        if cache_key in self._kpi_cache:
            cached_data = self._kpi_cache[cache_key]
            if time.time() - cached_data['timestamp'] < self._cache_ttl:
                logger.info(f"🚀 Using cached value for {kpi_id}")
                return cached_data['value']
            else:
                # Remove expired cache
                del self._kpi_cache[cache_key]
        return None

    def _set_cached_kpi_value(self, kpi_id: str, client_id: str, value: float):
        """Cache KPI value with timestamp."""
        cache_key = f"{client_id}:{kpi_id}"
        self._kpi_cache[cache_key] = {
            'value': value,
            'timestamp': time.time()
        }
        logger.info(f"💾 Cached value for {kpi_id}: {value}")

    def _get_cached_kpi_value_with_key(self, cache_key: str) -> Optional[float]:
        """Get cached KPI value using direct cache key."""
        if cache_key in self._kpi_cache:
            cached_data = self._kpi_cache[cache_key]
            if time.time() - cached_data['timestamp'] < self._cache_ttl:
                return cached_data['value']
            else:
                # Remove expired cache
                del self._kpi_cache[cache_key]
        return None

    def _set_cached_kpi_value_with_key(self, cache_key: str, value: float):
        """Cache KPI value with direct cache key."""
        self._kpi_cache[cache_key] = {
            'value': value,
            'timestamp': time.time()
        }
        logger.info(f"💾 Cached KPI value with key {cache_key}: {value}")

    def _get_cached_chart_data_with_filters(self, kpi_id: str, client_id: str, timeframe: str, currency: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached chart data for KPI with timeframe and currency filters."""
        cache_key = f"CHART:{client_id}:{kpi_id}:{timeframe}:{currency}"
        if cache_key in self._kpi_cache:
            cached_data = self._kpi_cache[cache_key]
            if time.time() - cached_data['timestamp'] < self._cache_ttl:
                logger.info(f"🚀 Using cached chart data for {kpi_id} (timeframe: {timeframe}, currency: {currency})")
                return cached_data['value']
            else:
                # Remove expired cache
                del self._kpi_cache[cache_key]
        return None

    def _set_cached_chart_data_with_filters(self, kpi_id: str, client_id: str, timeframe: str, currency: str, chart_data: List[Dict[str, Any]]):
        """Cache chart data with timeframe and currency filters."""
        cache_key = f"CHART:{client_id}:{kpi_id}:{timeframe}:{currency}"
        self._kpi_cache[cache_key] = {
            'value': chart_data,
            'timestamp': time.time()
        }
        logger.info(f"💾 Cached chart data with key {cache_key}: {len(chart_data)} points")

    def _get_timeframe_sql(self, timeframe: str) -> str:
        """
        Convert timeframe parameter to SQL WHERE clause for date filtering.
        
        Args:
            timeframe: One of '1d', 'week', 'month', 'quarter'
            
        Returns:
            SQL WHERE clause for date filtering
        """
        # Use relative dates from the most recent data in the database
        # This ensures filters work even with historical data
        timeframe_mapping = {
            '1d': "data_operacao >= (SELECT MAX(data_operacao) FROM boleta)",
            'week': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '7 days' FROM boleta)",
            'month': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '30 days' FROM boleta)", 
            'quarter': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '90 days' FROM boleta)"
        }
        
        sql_clause = timeframe_mapping.get(timeframe, timeframe_mapping['week'])
        logger.info(f"🕐 Timeframe filter '{timeframe}' converted to SQL: {sql_clause}")
        return sql_clause

    def _get_currency_sql(self, currency: str) -> str:
        """
        Convert currency parameter to SQL WHERE clause for currency filtering.
        
        Args:
            currency: One of 'all', 'usd', 'eur', 'gbp'
            
        Returns:
            SQL WHERE clause for currency filtering
        """
        if currency == 'all':
            return "1=1"  # No filter - include all currencies
            
        currency_mapping = {
            'usd': "moeda_principal = 'USD'",
            'eur': "moeda_principal = 'EUR'", 
            'gbp': "moeda_principal = 'GBP'"
        }
        
        sql_clause = currency_mapping.get(currency, "1=1")
        logger.info(f"💱 Currency filter '{currency}' converted to SQL: {sql_clause}")
        return sql_clause

    def _get_filter_aware_cache_key(self, kpi_id: str, client_id: str, timeframe: str = "week", currency: str = "all") -> str:
        """Generate cache key that includes filter parameters."""
        return f"VALUE:{client_id}:{kpi_id}:{timeframe}:{currency}"

    def _calculate_real_kpi_value(self, kpi_id: str, client_id: str = "L2M", timeframe: str = "week", currency: str = "all") -> Optional[float]:
        """
        Calculate real KPI value from client database using corrected queries with filter support.

        Args:
            kpi_id: KPI identifier
            client_id: Client identifier
            timeframe: Time period filter ('1d', 'week', 'month', 'quarter')
            currency: Currency filter ('all', 'usd', 'eur', 'gbp')

        Returns:
            Calculated KPI value or None if calculation fails
        """
        # Smart cache with filter-awareness and intelligent TTL
        cache_key = self._get_smart_cache_key(kpi_id, client_id, timeframe, currency, "VALUE")
        cached_value = self._get_from_smart_cache(cache_key)
        if cached_value is not None:
            logger.info(f"🎯 Smart cache HIT for {kpi_id} (timeframe={timeframe}, currency={currency})")
            return cached_value

        logger.info(f"🔍 Smart cache MISS for {kpi_id} (timeframe={timeframe}, currency={currency}) - calculating...")

        # Try to get dynamic query first
        dynamic_query = self.query_manager.get_kpi_query(kpi_id)
        
        if dynamic_query:
            logger.info(f"🎯 Using dynamic query for KPI {kpi_id}")
            try:
                # Execute dynamic query
                result = self._execute_dynamic_query(dynamic_query, client_id)
                if result is not None:
                    # Cache with smart TTL based on timeframe
                    ttl = self._get_intelligent_ttl(timeframe)
                    self._set_smart_cache(cache_key, result, ttl)
                    logger.info(f"💾 Cached dynamic result for {kpi_id} (TTL: {ttl}s)")
                    return result
                else:
                    logger.warning(f"⚠️ Dynamic query returned None for {kpi_id}, falling back to hardcoded")
            except Exception as e:
                logger.error(f"❌ Error executing dynamic query for {kpi_id}: {e}")
                # Continue to hardcoded fallback
        
        # Fallback to hardcoded queries
        logger.info(f"📌 Using hardcoded query for KPI {kpi_id}")
        
        try:
            # Import database tools
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from sqlalchemy import text

            # Connect to client database
            db_config = load_db_config(setor="cambio", cliente=client_id)
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            logger.info(f"✅ Connected to database for KPI calculation")

            # IMPORTANT: L2M is the exchange platform owner, not a client in the data
            # We should calculate KPIs for ALL transactions in the system, not filter by client_id
            
            # Get filter SQL clauses
            timeframe_sql = self._get_timeframe_sql(timeframe)
            currency_sql = self._get_currency_sql(currency)
            logger.info(f"🔍 Applying filters - Timeframe: {timeframe}, Currency: {currency}")

            with engine.connect() as conn:

                if kpi_id == 'total_volume':
                    # Total volume with filters applied
                    query = f'''
                        SELECT COALESCE(SUM(valor_me), 0) as volume_total
                        FROM boleta
                        WHERE valor_me IS NOT NULL
                        AND valor_me > 0
                        AND ({timeframe_sql})
                        AND ({currency_sql})
                    '''
                    logger.info(f"🔍 Executing total_volume query with filters: {query}")
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0
                    logger.info(f"📊 {kpi_id} filtered result: {value} (timeframe={timeframe}, currency={currency})")

                elif kpi_id == 'average_spread':
                    # Real average spread calculation with timeframe and currency filters
                    query = f'''
                        SELECT AVG(
                            CASE
                                WHEN tipo_operacao = 'VENDA' THEN
                                    ((taxa_cambio - taxa_base) / taxa_base) * 100
                                WHEN tipo_operacao = 'COMPRA' THEN
                                    ((taxa_base - taxa_cambio) / taxa_base) * 100
                                ELSE 0
                            END
                        ) as average_spread
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND taxa_cambio IS NOT NULL
                        AND taxa_base IS NOT NULL
                        AND taxa_base > 0
                        AND ({currency_sql})
                    '''
                    logger.info(f"🔢 Executing average_spread query with filters: {query}")
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0

                elif kpi_id == 'average_ticket':
                    # Real average ticket calculation with timeframe and currency filters
                    query = f'''
                        SELECT AVG(valor_me) as average_ticket
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND valor_me IS NOT NULL
                        AND valor_me > 0
                        AND ({currency_sql})
                    '''
                    logger.info(f"🔢 Executing average_ticket query with filters: {query}")
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0

                elif kpi_id == 'conversion_rate':
                    # Real conversion rate with timeframe and currency filters
                    query = f'''
                        SELECT
                            CASE
                                WHEN COUNT(*) > 0 THEN
                                    ROUND(
                                        COUNT(CASE WHEN id_boleta_status IN (4, 7) THEN 1 END) * 100.0 / COUNT(*),
                                        2
                                    )
                                ELSE 0
                            END as conversion_rate
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND ({currency_sql})
                    '''
                    logger.info(f"🔢 Executing conversion_rate query with filters: {query}")
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0

                elif kpi_id == 'retention_rate':
                    # Real client retention rate with timeframe and currency filters
                    query = f'''
                        WITH filtered_boletas AS (
                            SELECT id_cliente, data_operacao
                            FROM boleta
                            WHERE ({timeframe_sql})
                            AND ({currency_sql})
                        ),
                        max_date AS (SELECT MAX(data_operacao) as max_op FROM filtered_boletas),
                        current_clients AS (
                            SELECT DISTINCT id_cliente
                            FROM filtered_boletas f, max_date
                            WHERE f.data_operacao >= max_date.max_op - INTERVAL '15 days'
                        ),
                        previous_clients AS (
                            SELECT DISTINCT id_cliente
                            FROM filtered_boletas f, max_date
                            WHERE f.data_operacao >= max_date.max_op - INTERVAL '30 days'
                            AND f.data_operacao < max_date.max_op - INTERVAL '15 days'
                        )
                        SELECT
                            CASE
                                WHEN COUNT(p.id_cliente) > 0 THEN
                                    ROUND(
                                        COUNT(c.id_cliente) * 100.0 / COUNT(p.id_cliente),
                                        2
                                    )
                                ELSE 0
                            END as retention_rate
                        FROM previous_clients p
                        LEFT JOIN current_clients c ON p.id_cliente = c.id_cliente
                    '''
                    logger.info(f"🔢 Executing retention_rate query with filters: {query}")
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0

                elif kpi_id == 'gross_margin':
                    # Gross margin calculation with filters
                    query = f'''
                        SELECT 
                            CASE 
                                WHEN SUM(valor_me) > 0 THEN
                                    ROUND(
                                        (SUM(valor_me) - SUM(COALESCE(custo_operacao, valor_me * 0.02))) * 100.0 / SUM(valor_me),
                                        2
                                    )
                                ELSE 0
                            END as gross_margin
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND valor_me IS NOT NULL
                        AND valor_me > 0
                        AND ({currency_sql})
                    '''
                    logger.info(f"🔢 Executing gross_margin query with filters: {query}")
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0

                else:
                    # For other KPIs, return a calculated value based on operations with filters
                    query = f'''
                        SELECT COUNT(*) as operations
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND ({currency_sql})
                    '''
                    logger.info(f"🔢 Executing fallback query for {kpi_id} with filters: {query}")
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0

                # Cache with smart TTL based on timeframe
                ttl = self._get_intelligent_ttl(timeframe)
                self._set_smart_cache(cache_key, value, ttl)
                logger.info(f"💾 Cached hardcoded result for {kpi_id} (TTL: {ttl}s)")
                return value

        except Exception as e:
            logger.error(f"❌ Error calculating real value for KPI {kpi_id}: {e}")
            return None

    def _execute_dynamic_query(self, query: str, client_id: str) -> Optional[float]:
        """
        Execute a dynamically generated KPI query.
        
        Args:
            query: SQL query to execute
            client_id: Client identifier
            
        Returns:
            Query result as float or None
        """
        try:
            # Map L2M to correct client ID (from JSON config)
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            
            # Replace :client_id placeholder with actual value
            query_with_params = query.replace(':client_id', str(numeric_client_id))
            
            # Get connection and execute
            conn = self._get_db_connection(client_id)
            if not conn:
                logger.error("Failed to get database connection")
                return None
                
            result = conn.execute(text(query_with_params))
            row = result.fetchone()
            
            if row and row[0] is not None:
                return float(row[0])
            else:
                logger.warning("Query returned no results")
                return 0.0
                
        except Exception as e:
            logger.error(f"❌ Error executing dynamic query: {e}")
            return None

    def _get_db_connection(self, client_id: str):
        """Get database connection for client."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            
            db_config = load_db_config(setor="cambio", cliente=client_id)
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            
            return engine.connect()
        except Exception as e:
            logger.error(f"Failed to get DB connection for {client_id}: {e}")
            return None
    
    def _get_fallback_kpi_queries(self) -> Dict[str, str]:
        """Get hardcoded fallback queries for KPIs using smart date detection."""
        return {
            'total_volume': '''
                SELECT COALESCE(SUM(valor_me), 0) as total_volume
                FROM boleta
                WHERE data_operacao >= (SELECT MAX(data_operacao) FROM boleta) - INTERVAL '12 months'
                AND id_cliente = :client_id
                AND valor_me IS NOT NULL
            ''',
            'average_spread': '''
                SELECT AVG(
                    CASE
                        WHEN tipo_operacao = 'VENDA' THEN
                            ((taxa_cambio - taxa_base) / taxa_base) * 100
                        WHEN tipo_operacao = 'COMPRA' THEN
                            ((taxa_base - taxa_cambio) / taxa_base) * 100
                        ELSE 0
                    END
                ) as average_spread
                FROM boleta
                WHERE data_operacao >= (SELECT MAX(data_operacao) FROM boleta) - INTERVAL '6 months'
                AND id_cliente = :client_id
                AND taxa_cambio IS NOT NULL
                AND taxa_base IS NOT NULL
                AND taxa_base > 0
            ''',
            'average_ticket': '''
                SELECT AVG(valor_me) as average_ticket
                FROM boleta
                WHERE data_operacao >= (SELECT MAX(data_operacao) FROM boleta) - INTERVAL '12 months'
                AND id_cliente = :client_id
                AND valor_me IS NOT NULL
                AND valor_me > 0
            ''',
            'conversion_rate': '''
                SELECT
                    CASE
                        WHEN COUNT(*) > 0 THEN
                            ROUND(
                                COUNT(CASE WHEN id_boleta_status IN (4, 7) THEN 1 END) * 100.0 / COUNT(*),
                                2
                            )
                        ELSE 0
                    END as conversion_rate
                FROM boleta
                WHERE data_operacao >= (SELECT MAX(data_operacao) FROM boleta) - INTERVAL '12 months'
                AND id_cliente = :client_id
            '''
        }

    def calculate_single_kpi(self, kpi_id: str, sector: str = "cambio", client_id: str = "L2M") -> Optional[Dict[str, Any]]:
        """
        Calculate a single KPI by ID.
        
        Args:
            kpi_id: KPI identifier
            sector: Business sector
            client_id: Client identifier
            
        Returns:
            Dictionary with calculated KPI data or None if not found
        """
        try:
            logger.info(f"🎯 Calculating single KPI: {kpi_id} for {client_id}/{sector}")
            
            # Get KPI definitions
            kpi_definitions = self.get_kpi_definitions(sector=sector)
            
            # Find the specific KPI
            kpi_def = None
            for kpi in kpi_definitions:
                if kpi['id'] == kpi_id:
                    kpi_def = kpi
                    break
            
            if not kpi_def:
                logger.warning(f"KPI {kpi_id} not found in definitions")
                return None
            
            # Calculate the KPI value
            result = self.calculate_kpi_value_from_dict(kpi_def, client_id)
            
            if result:
                logger.info(f"✅ Successfully calculated KPI {kpi_id}: {result['currentValue']}")
            else:
                logger.error(f"❌ Failed to calculate KPI {kpi_id}")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ Error calculating single KPI {kpi_id}: {e}")
            return None

    def get_dashboard_kpis(self, sector: str = "cambio", client_id: str = "L2M", timeframe: str = "1d", category: Optional[str] = None, priority_only: bool = True, currency: str = "all") -> List[Dict[str, Any]]:
        """
        Get all dashboard KPIs with calculated values.
        
        Args:
            sector: Business sector
            client_id: Client identifier
            timeframe: Time frame for calculations (now implemented with SQL filters)
            category: Filter by KPI category
            priority_only: If True, load only priority KPIs for faster response
            currency: Currency filter for calculations
            
        Returns:
            List of calculated KPI data
        """
        try:
            logger.info(f"📊 Getting dashboard KPIs for {client_id}/{sector} with FILTERS - timeframe: {timeframe}, currency: {currency}, priority_only: {priority_only}")
            
            # Get KPI definitions with optional category filter
            kpi_definitions = self.get_kpi_definitions(sector=sector, category=category)
            
            if not kpi_definitions:
                logger.warning(f"No KPI definitions found for sector {sector}")
                return []
            
            # Filter to priority KPIs only if requested
            if priority_only:
                priority_kpi_definitions = [kpi for kpi in kpi_definitions if kpi.get('is_priority', False)]
                logger.info(f"🎯 Filtering to {len(priority_kpi_definitions)} priority KPIs (out of {len(kpi_definitions)} total)")
                kpi_definitions = priority_kpi_definitions
            
            # Calculate all KPIs
            calculated_kpis = []
            success_count = 0
            
            for kpi_def in kpi_definitions:
                try:
                    kpi_result = self.calculate_kpi_value_from_dict(kpi_def, client_id, timeframe, currency)
                    if kpi_result:
                        calculated_kpis.append(kpi_result)
                        success_count += 1
                    else:
                        logger.warning(f"Failed to calculate KPI {kpi_def['id']}")
                        # Add error entry for failed KPI
                        error_kpi = {
                            'id': kpi_def['id'],
                            'title': kpi_def['name'],
                            'description': kpi_def['description'],
                            'currentValue': 0,
                            'format': kpi_def['format_type'],
                            'changePercent': None,
                            'trend': 'stable',
                            'chartType': kpi_def['chart_type'],
                            'chartData': [],
                            'alert': None,
                            'isPriority': kpi_def['is_priority'],
                            'order': kpi_def['display_order'],
                            'category': kpi_def['category'],
                            'unit': kpi_def['unit'],
                            'frequency': kpi_def['frequency'],
                            'status': 'error',
                            'error_message': 'Calculation failed'
                        }
                        calculated_kpis.append(error_kpi)
                        
                except Exception as e:
                    logger.error(f"Error calculating KPI {kpi_def['id']}: {e}")
                    # Add error entry for exception
                    error_kpi = {
                        'id': kpi_def['id'],
                        'title': kpi_def['name'],
                        'description': kpi_def['description'],
                        'currentValue': 0,
                        'format': kpi_def['format_type'],
                        'changePercent': None,
                        'trend': 'stable',
                        'chartType': kpi_def['chart_type'],
                        'chartData': [],
                        'alert': None,
                        'isPriority': kpi_def['is_priority'],
                        'order': kpi_def['display_order'],
                        'category': kpi_def['category'],
                        'unit': kpi_def['unit'],
                        'frequency': kpi_def['frequency'],
                        'status': 'error',
                        'error_message': f'Calculation failed: {str(e)}'
                    }
                    calculated_kpis.append(error_kpi)
            
            logger.info(f"✅ Calculated {success_count}/{len(kpi_definitions)} KPIs successfully (priority_only: {priority_only})")
            return calculated_kpis
            
        except Exception as e:
            logger.error(f"❌ Error getting dashboard KPIs: {e}")
            return []

    def invalidate_kpi_cache(self, kpi_ids: List[str], client_id: str = "L2M", timeframe: Optional[str] = None, currency: Optional[str] = None):
        """
        Invalidate cache for specific KPIs.
        
        Args:
            kpi_ids: List of KPI IDs to invalidate
            client_id: Client identifier
            timeframe: Optional timeframe filter (None = all timeframes)
            currency: Optional currency filter (None = all currencies)
        """
        invalidated_count = 0
        
        # If no specific filters, invalidate all combinations
        if timeframe is None and currency is None:
            timeframes = ['1d', 'week', 'month', 'quarter']
            currencies = ['all', 'usd', 'eur', 'gbp']
        else:
            timeframes = [timeframe] if timeframe else ['1d', 'week', 'month', 'quarter']
            currencies = [currency] if currency else ['all', 'usd', 'eur', 'gbp']
        
        # Invalidate for all combinations
        for kpi_id in kpi_ids:
            for tf in timeframes:
                for curr in currencies:
                    # Invalidate VALUE cache
                    value_key = self._get_smart_cache_key(kpi_id, client_id, tf, curr, "VALUE")
                    if value_key in self._kpi_cache:
                        del self._kpi_cache[value_key]
                        invalidated_count += 1
                    if value_key in self._cache_metadata:
                        del self._cache_metadata[value_key]
                    
                    # Invalidate CHART cache
                    chart_key = self._get_smart_cache_key(kpi_id, client_id, tf, curr, "CHART")
                    if chart_key in self._kpi_cache:
                        del self._kpi_cache[chart_key]
                        invalidated_count += 1
                    if chart_key in self._cache_metadata:
                        del self._cache_metadata[chart_key]
        
        logger.info(f"🗑️ Invalidated {invalidated_count} cache entries for {len(kpi_ids)} KPIs")
        return invalidated_count
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for cache keys."""
        return datetime.now().strftime("%Y%m%d%H%M%S")


# Singleton instance
_kpi_service_instance = None


def get_kpi_service() -> KpiCalculationService:
    """Get singleton instance of KPI calculation service."""
    global _kpi_service_instance
    if _kpi_service_instance is None:
        _kpi_service_instance = KpiCalculationService()
    return _kpi_service_instance
