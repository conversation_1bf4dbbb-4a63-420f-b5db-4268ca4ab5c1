"""
Cache Warming Service for DataHero4 Dashboard
Pré-carrega KPIs críticos para melhorar performance do dashboard.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import time

logger = logging.getLogger(__name__)


class CacheWarmingService:
    """
    Serviço responsável por pré-carregar cache dos KPIs mais críticos
    para garantir performance otimizada do dashboard.
    """
    
    def __init__(self):
        """Inicializar o serviço de cache warming."""
        self.kpi_service = None  # Lazy loading para evitar circular imports
        self.priority_kpis = [
            'total_volume',
            'average_ticket', 
            'retention_rate',
            'conversion_rate',
            'average_spread'
        ]
        self.timeframes = ['1d', 'week', 'month', 'quarter']
        self.currencies = ['all', 'usd', 'eur', 'gbp']
        self.warming_in_progress = False
        
    def _get_kpi_service(self):
        """Lazy loading do KPI service para evitar imports circulares."""
        if self.kpi_service is None:
            from src.services.kpi_service_refactored import get_kpi_service_refactored
            self.kpi_service = get_kpi_service_refactored()
        return self.kpi_service
        
    async def warm_priority_kpis(self, client_id: str = "L2M") -> Dict[str, Any]:
        """
        Pré-carrega cache dos KPIs prioritários para todas as combinações de filtros.
        
        Args:
            client_id: ID do cliente para warming
            
        Returns:
            Relatório do warming executado
        """
        if self.warming_in_progress:
            logger.info("🔥 Cache warming já em progresso, ignorando nova solicitação")
            return {"status": "skipped", "reason": "warming_in_progress"}
            
        self.warming_in_progress = True
        start_time = time.time()
        
        try:
            logger.info(f"🔥 Iniciando cache warming para cliente {client_id}")
            
            kpi_service = self._get_kpi_service()
            warming_results = {
                'client_id': client_id,
                'started_at': datetime.now().isoformat(),
                'kpis_warmed': 0,
                'combinations_warmed': 0,
                'errors': [],
                'duration_seconds': 0
            }
            
            # Warm KPI values (sem filtros específicos)
            for kpi_id in self.priority_kpis:
                try:
                    logger.info(f"🔥 Warming KPI value: {kpi_id}")
                    value = kpi_service._calculate_real_kpi_value(kpi_id, client_id)
                    if value is not None:
                        warming_results['kpis_warmed'] += 1
                        logger.info(f"✅ KPI {kpi_id} warmed: {value}")
                    else:
                        logger.warning(f"⚠️ KPI {kpi_id} returned None")
                        
                except Exception as e:
                    error_msg = f"Error warming KPI {kpi_id}: {e}"
                    logger.error(f"❌ {error_msg}")
                    warming_results['errors'].append(error_msg)
            
            # Warm chart data para todas as combinações de filtros
            for kpi_id in self.priority_kpis:
                for timeframe in self.timeframes:
                    for currency in self.currencies:
                        try:
                            logger.info(f"🔥 Warming chart data: {kpi_id} (timeframe: {timeframe}, currency: {currency})")
                            
                            # Get KPI metadata para format_type
                            kpi_definitions = kpi_service.get_kpi_definitions(sector="cambio", active_only=True)
                            kpi_def = None
                            for category in kpi_definitions:
                                for kpi in category.get('kpis', []):
                                    if kpi['id'] == kpi_id:
                                        kpi_def = kpi
                                        break
                                if kpi_def:
                                    break
                            
                            if kpi_def:
                                format_type = kpi_def.get('format_type', 'number')
                                chart_data = kpi_service._generate_real_chart_data(
                                    kpi_id, format_type, client_id, timeframe
                                )
                                
                                if chart_data:
                                    warming_results['combinations_warmed'] += 1
                                    logger.info(f"✅ Chart data warmed: {kpi_id} ({timeframe}, {currency}) - {len(chart_data)} points")
                                    
                                    # Small delay to avoid overwhelming the database
                                    await asyncio.sleep(0.1)
                            else:
                                logger.warning(f"⚠️ KPI definition not found for {kpi_id}")
                                
                        except Exception as e:
                            error_msg = f"Error warming chart data {kpi_id} ({timeframe}, {currency}): {e}"
                            logger.error(f"❌ {error_msg}")
                            warming_results['errors'].append(error_msg)
            
            warming_results['duration_seconds'] = round(time.time() - start_time, 2)
            warming_results['completed_at'] = datetime.now().isoformat()
            
            logger.info(f"🔥 Cache warming concluído em {warming_results['duration_seconds']}s")
            logger.info(f"📊 Resultados: {warming_results['kpis_warmed']} KPIs, {warming_results['combinations_warmed']} combinações")
            
            if warming_results['errors']:
                logger.warning(f"⚠️ {len(warming_results['errors'])} erros durante warming")
            
            return warming_results
            
        except Exception as e:
            logger.error(f"❌ Erro crítico durante cache warming: {e}")
            return {
                "status": "error", 
                "error": str(e),
                "duration_seconds": round(time.time() - start_time, 2)
            }
        finally:
            self.warming_in_progress = False
    
    def warm_priority_kpis_sync(self, client_id: str = "L2M") -> Dict[str, Any]:
        """
        Versão síncrona do cache warming para uso em scripts e cron jobs.
        
        Args:
            client_id: ID do cliente para warming
            
        Returns:
            Relatório do warming executado
        """
        try:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.warm_priority_kpis(client_id))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"❌ Erro executando cache warming síncrono: {e}")
            return {"status": "error", "error": str(e)}
    
    def should_warm_cache(self, client_id: str = "L2M") -> bool:
        """
        Determina se o cache deve ser aquecido baseado em heurísticas.
        
        Args:
            client_id: ID do cliente
            
        Returns:
            True se o cache deve ser aquecido
        """
        try:
            kpi_service = self._get_kpi_service()
            
            # Check se algum KPI prioritário não está em cache
            for kpi_id in self.priority_kpis[:3]:  # Check apenas os 3 mais críticos
                cached_value = kpi_service._get_cached_kpi_value(kpi_id, client_id)
                if cached_value is None:
                    logger.info(f"🔥 Cache warming necessário: KPI {kpi_id} não está em cache")
                    return True
            
            # Check se chart data para timeframe padrão não está em cache
            for kpi_id in self.priority_kpis[:2]:  # Check apenas os 2 mais críticos
                cached_chart = kpi_service._get_cached_chart_data_with_filters(
                    kpi_id, client_id, "week", "all"
                )
                if cached_chart is None:
                    logger.info(f"🔥 Cache warming necessário: Chart data {kpi_id} não está em cache")
                    return True
            
            logger.info("✅ Cache está aquecido, warming não necessário")
            return False
            
        except Exception as e:
            logger.error(f"❌ Erro verificando necessidade de warming: {e}")
            return True  # Em caso de erro, execute warming por segurança
    
    def get_cache_status(self, client_id: str = "L2M") -> Dict[str, Any]:
        """
        Retorna status detalhado do cache para monitoramento.
        
        Args:
            client_id: ID do cliente
            
        Returns:
            Status detalhado do cache
        """
        try:
            kpi_service = self._get_kpi_service()
            
            status = {
                'client_id': client_id,
                'timestamp': datetime.now().isoformat(),
                'kpi_values_cached': 0,
                'chart_data_cached': 0,
                'total_priority_kpis': len(self.priority_kpis),
                'cache_coverage_percent': 0,
                'warming_in_progress': self.warming_in_progress
            }
            
            # Check KPI values
            for kpi_id in self.priority_kpis:
                cached_value = kpi_service._get_cached_kpi_value(kpi_id, client_id)
                if cached_value is not None:
                    status['kpi_values_cached'] += 1
            
            # Check chart data (apenas timeframe padrão)
            for kpi_id in self.priority_kpis:
                cached_chart = kpi_service._get_cached_chart_data_with_filters(
                    kpi_id, client_id, "week", "all"
                )
                if cached_chart is not None:
                    status['chart_data_cached'] += 1
            
            # Calculate coverage percentage
            total_items = len(self.priority_kpis) * 2  # values + chart data
            cached_items = status['kpi_values_cached'] + status['chart_data_cached']
            status['cache_coverage_percent'] = round((cached_items / total_items) * 100, 1)
            
            return status
            
        except Exception as e:
            logger.error(f"❌ Erro obtendo status do cache: {e}")
            return {
                'client_id': client_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# Singleton instance
_cache_warming_service = None

def get_cache_warming_service() -> CacheWarmingService:
    """Get singleton instance of cache warming service."""
    global _cache_warming_service
    if _cache_warming_service is None:
        _cache_warming_service = CacheWarmingService()
    return _cache_warming_service 