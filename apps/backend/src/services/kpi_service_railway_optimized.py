"""
KPI Service Optimized for Railway with Redis
===========================================

Versão otimizada usando Redis do Railway:
- Usa o Redis já configurado no Railway
- Paralelização com ThreadPoolExecutor
- <PERSON><PERSON> hierárquico completo (L1 Memory + L2 Redis)
- Meta: < 2 segundos de resposta
"""

import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

from src.services.kpi_service import KpiCalculationService
from src.services.snapshot_service import SnapshotService
from src.caching.hierarchical_cache import get_hierarchical_cache
from src.caching.redis_cache_manager import get_redis_manager

logger = logging.getLogger(__name__)


class RailwayOptimizedKpiService(KpiCalculationService):
    """Serviço otimizado para Railway usando Redis existente."""
    
    def __init__(self):
        super().__init__()
        self.executor = ThreadPoolExecutor(max_workers=6)  # Aumentado para 6
        self.snapshot_service = SnapshotService()
        self.hierarchical_cache = get_hierarchical_cache()
        self.redis_manager = get_redis_manager()
        
        # TTLs otimizados
        self._ttl_config = {
            '1d': 300,      # 5 minutos
            'week': 900,    # 15 minutos  
            'month': 1800,  # 30 minutos
            'quarter': 3600 # 1 hora
        }
        
        # Log Redis status
        if self.redis_manager.is_connected():
            logger.info("✅ Redis conectado com sucesso para KPI service")
        else:
            logger.warning("⚠️ Redis não disponível, usando apenas cache L1")
    
    def get_dashboard_kpis_fast(
        self,
        sector: str = "cambio",
        client_id: str = "L2M",
        timeframe: str = "week",
        category: Optional[str] = None,
        priority_only: bool = True,
        currency: str = "all"
    ) -> Dict[str, Any]:
        """
        Versão rápida do get_dashboard_kpis com Redis.
        Meta: < 2 segundos de resposta.
        """
        start_time = time.time()
        
        # Criar query sintética para cache hierárquico
        query_key = f"dashboard_kpis_{timeframe}_{currency}_{priority_only}"
        
        # 1. Tentar cache hierárquico (L1 + L2 Redis)
        cached_result = self.hierarchical_cache.get_query_cache(
            question=query_key,
            client_id=client_id,
            sector=sector
        )
        
        if cached_result:
            response_time = round((time.time() - start_time) * 1000, 2)
            cached_result['response_time_ms'] = response_time
            cached_result['cache_source'] = 'hierarchical'
            logger.info(f"✅ Cache hit: {response_time}ms")
            return cached_result
        
        # 2. Se não tem cache, verificar snapshot para resposta imediata
        if priority_only and self._should_use_snapshot(timeframe, currency):
            snapshot_data = self._get_snapshot_fallback()
            if snapshot_data:
                logger.info("📸 Using snapshot for immediate response")
                # Salvar snapshot no cache para próximas requisições
                self._cache_result(query_key, client_id, sector, snapshot_data, timeframe)
                return snapshot_data
        
        # 3. Calcular KPIs em paralelo
        logger.info(f"🔍 Cache miss, calculating KPIs in parallel...")
        kpis = self._calculate_kpis_parallel(
            sector, client_id, timeframe, category, priority_only, currency
        )
        
        # 4. Preparar resposta
        processing_time = round((time.time() - start_time) * 1000, 2)
        
        result = {
            "kpis": kpis,
            "total_count": len(kpis),
            "sector": sector,
            "client_id": client_id,
            "timeframe": timeframe,
            "currency": currency,
            "priority_only": priority_only,
            "generated_at": datetime.now().isoformat(),
            "processing_time_ms": processing_time,
            "cache_source": "calculated",
            "redis_connected": self.redis_manager.is_connected()
        }
        
        # 5. Salvar em cache
        self._cache_result(query_key, client_id, sector, result, timeframe)
        
        logger.info(f"✅ Calculated {len(kpis)} KPIs in {processing_time}ms")
        return result
    
    def _cache_result(self, query_key: str, client_id: str, sector: str, 
                     result: Dict[str, Any], timeframe: str):
        """Salva resultado no cache hierárquico."""
        ttl = self._ttl_config.get(timeframe, 900)
        
        self.hierarchical_cache.set_query_cache(
            question=query_key,
            client_id=client_id,
            sector=sector,
            result=result,
            ttl_override=ttl
        )
    
    def _should_use_snapshot(self, timeframe: str, currency: str) -> bool:
        """Determina se deve usar snapshot como fallback."""
        return timeframe == "week" and currency == "all"
    
    def _get_snapshot_fallback(self) -> Optional[Dict[str, Any]]:
        """Obtém dados do snapshot como fallback rápido."""
        try:
            snapshot = self.snapshot_service.get_latest_snapshot()
            if snapshot and snapshot.get('kpis'):
                kpis = []
                for kpi_id, kpi_data in snapshot['kpis'].items():
                    kpis.append({
                        'id': kpi_id,
                        'title': kpi_data.get('title', kpi_id),
                        'description': kpi_data.get('description', ''),
                        'currentValue': kpi_data.get('value', 0),
                        'format': kpi_data.get('format', 'number'),
                        'changePercent': 0,
                        'trend': 'stable',
                        'chartType': 'line',
                        'chartData': [],
                        'isPriority': True,
                        'order': 0,
                        'category': kpi_data.get('category', 'general')
                    })
                
                return {
                    "kpis": kpis[:6],
                    "total_count": len(kpis),
                    "generated_at": snapshot['metadata'].get('date', ''),
                    "cache_source": "snapshot",
                    "response_time_ms": 50,
                    "redis_connected": self.redis_manager.is_connected()
                }
        except Exception as e:
            logger.error(f"❌ Error getting snapshot: {e}")
        return None
    
    def _calculate_kpis_parallel(
        self,
        sector: str,
        client_id: str,
        timeframe: str,
        category: Optional[str],
        priority_only: bool,
        currency: str
    ) -> List[Dict[str, Any]]:
        """Calcula KPIs em paralelo."""
        # Obter definições
        kpi_definitions = self.get_kpi_definitions(sector=sector, category=category)
        
        if priority_only:
            # KPIs críticos apenas
            critical_ids = [
                'total_volume', 'average_ticket', 'average_spread',
                'conversion_rate', 'retention_rate', 'gross_margin'
            ]
            kpi_definitions = [
                kpi for kpi in kpi_definitions 
                if kpi.get('id') in critical_ids
            ]
        
        # Limitar a 6 KPIs
        kpi_definitions = kpi_definitions[:6]
        
        # Calcular em paralelo
        results = []
        future_to_kpi = {}
        
        for kpi_def in kpi_definitions:
            future = self.executor.submit(
                self._calculate_single_kpi_cached,
                kpi_def, client_id, timeframe, currency
            )
            future_to_kpi[future] = kpi_def
        
        # Coletar resultados
        for future in as_completed(future_to_kpi):
            kpi_def = future_to_kpi[future]
            try:
                result = future.result(timeout=5)
                if result:
                    results.append(result)
            except Exception as e:
                logger.error(f"❌ Error calculating {kpi_def.get('id')}: {e}")
                # Adicionar placeholder
                results.append(self._get_error_placeholder(kpi_def))
        
        # Ordenar por prioridade
        results.sort(key=lambda x: x.get('order', 999))
        return results
    
    def _calculate_single_kpi_cached(
        self,
        kpi_dict: Dict[str, Any],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """Calcula um KPI com cache individual."""
        kpi_id = kpi_dict.get('id', 'unknown')
        
        # Tentar cache específico do KPI primeiro
        cache_key = f"kpi_value:{kpi_id}:{client_id}:{timeframe}:{currency}"
        
        if self.redis_manager.is_connected():
            cached = self.redis_manager.get(cache_key)
            if cached:
                logger.debug(f"🎯 Redis hit for KPI {kpi_id}")
                return cached
        
        try:
            # Calcular valor atual com filtros
            current_value = self._calculate_real_kpi_value(kpi_id, client_id, timeframe, currency)
            
            # Calcular valor do período anterior para change percent
            previous_value = self._calculate_previous_period_value(kpi_id, client_id, timeframe, currency)
            
            # Calcular percentual de mudança real
            change_percent = None
            trend = 'stable'
            
            if current_value is not None and previous_value is not None and previous_value > 0:
                change_percent = ((current_value - previous_value) / previous_value) * 100
                trend = 'up' if change_percent > 0 else 'down' if change_percent < 0 else 'stable'
                logger.info(f"📊 {kpi_id} change: current={current_value}, previous={previous_value}, change={change_percent:.1f}%")
            
            # Gerar dados do gráfico
            chart_data = self._generate_real_chart_data(
                kpi_id, kpi_dict.get('format_type', 'number'), 
                client_id, timeframe, currency
            )
            
            # Montar resultado completo
            result = {
                'id': kpi_id,
                'title': kpi_dict.get('name', ''),
                'description': kpi_dict.get('description', ''),
                'currentValue': current_value if current_value is not None else 0,
                'format': kpi_dict.get('format_type', 'number'),
                'changePercent': round(change_percent, 1) if change_percent is not None else None,
                'trend': trend,
                'chartType': 'line',
                'chartData': chart_data or [],
                'isPriority': kpi_dict.get('is_priority', False),
                'order': kpi_dict.get('display_order', 999),
                'category': kpi_dict.get('category', 'general')
            }
            
            if result and self.redis_manager.is_connected():
                # Cachear resultado individual
                ttl = self._ttl_config.get(timeframe, 900)
                self.redis_manager.set(cache_key, result, ttl)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error calculating {kpi_id}: {e}")
            return self._get_error_placeholder(kpi_dict)
    
    def _calculate_previous_period_value(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[float]:
        """Calcula o valor do período anterior para comparação."""
        # Mapear timeframe para período anterior
        previous_timeframe_sql = {
            '1d': "data_operacao = (SELECT MAX(data_operacao) - INTERVAL '1 day' FROM boleta)",
            'week': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '14 days' FROM boleta) AND data_operacao < (SELECT MAX(data_operacao) - INTERVAL '7 days' FROM boleta)",
            'month': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '60 days' FROM boleta) AND data_operacao < (SELECT MAX(data_operacao) - INTERVAL '30 days' FROM boleta)",
            'quarter': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '180 days' FROM boleta) AND data_operacao < (SELECT MAX(data_operacao) - INTERVAL '90 days' FROM boleta)"
        }
        
        # Criar cache key para período anterior
        cache_key = f"kpi_value_prev:{kpi_id}:{client_id}:{timeframe}:{currency}"
        
        # Verificar cache
        if self.redis_manager.is_connected():
            cached = self.redis_manager.get(cache_key)
            if cached and isinstance(cached, dict):
                return cached.get('value')
        
        try:
            # Import database tools
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from sqlalchemy import text

            # Connect to client database
            db_config = load_db_config(setor="cambio", cliente=client_id)
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            
            # Get SQL for previous period
            prev_sql = previous_timeframe_sql.get(timeframe, previous_timeframe_sql['week'])
            currency_sql = self._get_currency_sql(currency)
            
            with engine.connect() as conn:
                value = None
                
                if kpi_id == 'total_volume':
                    query = f'''
                        SELECT COALESCE(SUM(valor_me), 0) as volume_total
                        FROM boleta
                        WHERE valor_me IS NOT NULL
                        AND valor_me > 0
                        AND ({prev_sql})
                        AND ({currency_sql})
                    '''
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0
                    
                elif kpi_id == 'average_ticket':
                    query = f'''
                        SELECT COALESCE(AVG(valor_me), 0) as avg_ticket
                        FROM boleta
                        WHERE ({prev_sql})
                        AND valor_me > 0
                        AND ({currency_sql})
                    '''
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0
                    
                elif kpi_id == 'average_spread':
                    query = f'''
                        SELECT COALESCE(AVG(spread), 0) as avg_spread
                        FROM boleta
                        WHERE ({prev_sql})
                        AND spread IS NOT NULL
                        AND ({currency_sql})
                    '''
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    value = float(row[0]) if row and row[0] else 0
                
                # Cache result
                if value is not None and self.redis_manager.is_connected():
                    ttl = self._ttl_config.get(timeframe, 900)
                    self.redis_manager.set(cache_key, {'value': value}, ttl)
                
                return value
                
        except Exception as e:
            logger.error(f"❌ Error calculating previous period for {kpi_id}: {e}")
            return None
    
    def _get_error_placeholder(self, kpi_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Retorna placeholder em caso de erro."""
        return {
            'id': kpi_dict.get('id', 'unknown'),
            'title': kpi_dict.get('name', 'Error'),
            'description': kpi_dict.get('description', ''),
            'currentValue': 0,
            'format': kpi_dict.get('format_type', 'number'),
            'changePercent': None,
            'trend': 'stable',
            'chartType': 'line',
            'chartData': [],
            'isPriority': kpi_dict.get('is_priority', False),
            'order': kpi_dict.get('display_order', 999),
            'error': True
        }
    
    def cleanup(self):
        """Limpa recursos."""
        self.executor.shutdown(wait=True)
        logger.info("✅ Railway optimized service cleaned up")


# Singleton
_railway_service_instance = None

def get_railway_optimized_service() -> RailwayOptimizedKpiService:
    """Retorna instância singleton do serviço otimizado."""
    global _railway_service_instance
    if _railway_service_instance is None:
        _railway_service_instance = RailwayOptimizedKpiService()
    return _railway_service_instance 