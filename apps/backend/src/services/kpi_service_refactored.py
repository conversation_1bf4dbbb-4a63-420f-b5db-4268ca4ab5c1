"""
KPI Service Refatorado - DataHero4
==================================

Versão refatorada do serviço de KPIs usando o sistema de cache unificado.
Remove todas as implementações de cache legadas.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
try:
    from sqlalchemy import text
except ImportError:
    # Fallback se sqlalchemy não estiver disponível
    text = lambda x: x

from src.models.learning_models import KpiDefinition
from src.utils.learning_db_utils import get_db_manager
from src.caching.unified_cache_system import get_unified_cache
from src.services.kpi_query_manager_json import KpiQueryManagerJSON

logger = logging.getLogger(__name__)


class KpiCalculationServiceRefactored:
    """Serviço refatorado para cálculo e formatação de KPIs."""
    
    def __init__(self):
        # Cache unificado
        self.cache = get_unified_cache()
        
        # Database manager
        self.db_manager = None
        self._init_db_connection()
        
        # Query manager
        self.query_manager = KpiQueryManagerJSON(sector="cambio")
        
        logger.info("✅ KPI Service Refatorado inicializado com cache unificado")
    
    def _init_db_connection(self):
        """Inicializa conexão com banco de dados."""
        try:
            self.db_manager = get_db_manager()
            logger.info("✅ KPI Service: Usando learning database manager")
        except Exception as e:
            logger.warning(f"Learning DB não disponível: {e}")
            # Fallback implementation se necessário
            self.db_manager = None
    
    def get_dashboard_kpis(
        self,
        sector: str = "cambio",
        client_id: str = "L2M", 
        timeframe: str = "week",
        category: Optional[str] = None,
        priority_only: bool = True,
        currency: str = "all"
    ) -> List[Dict[str, Any]]:
        """
        Retorna KPIs formatados para o dashboard com suporte a filtros.
        
        Args:
            sector: Setor de negócio
            client_id: ID do cliente
            timeframe: Período ('1d', 'week', 'month', 'quarter')
            category: Categoria específica (opcional)
            priority_only: Apenas KPIs prioritários
            currency: Filtro de moeda ('all', 'usd', 'eur', 'gbp')
            
        Returns:
            Lista de KPIs formatados
        """
        logger.info(f"📊 Calculando KPIs - timeframe: {timeframe}, currency: {currency}")
        
        # Tentar cache primeiro
        cached_result = self.cache.get(
            "kpi:dashboard",
            client_id=client_id,
            timeframe=timeframe,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        if cached_result is not None:
            logger.info(f"✅ Dashboard KPIs do cache: {len(cached_result)} KPIs")
            return cached_result
        
        # Calcular KPIs
        kpis = []
        
        # Carregar definições de KPIs
        kpi_definitions = self._load_kpi_definitions(
            sector=sector,
            category=category,
            priority_only=priority_only
        )
        
        for kpi_def in kpi_definitions:
            try:
                kpi_data = self._calculate_single_kpi(
                    kpi_def=kpi_def,
                    client_id=client_id,
                    timeframe=timeframe,
                    currency=currency
                )
                
                if kpi_data:
                    kpis.append(kpi_data)
                    
            except Exception as e:
                logger.error(f"❌ Erro calculando KPI {kpi_def.get('id')}: {e}")
        
        # Cachear resultado
        self.cache.set(
            "kpi:dashboard",
            kpis,
            timeframe=timeframe,
            client_id=client_id,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        logger.info(f"✅ Calculados {len(kpis)} KPIs com sucesso")
        return kpis
    
    def _calculate_single_kpi(
        self,
        kpi_def: Dict[str, Any],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """Calcula um único KPI."""
        kpi_id = kpi_def.get('id')
        
        # Calcular valor
        value = self._calculate_kpi_value(
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        if value is None:
            return None
        
        # Gerar dados do gráfico
        chart_data = self._generate_chart_data(
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        # Formatar resposta
        return {
            'id': kpi_id,
            'title': kpi_def.get('name', ''),
            'description': kpi_def.get('description', ''),
            'currentValue': value,
            'format': kpi_def.get('format_type', 'number'),
            'changePercent': self._calculate_change_percent(kpi_id, value, timeframe),
            'trend': self._determine_trend(kpi_id, value),
            'chartType': kpi_def.get('chart_type', 'line'),
            'chartData': chart_data,
            'alert': self._check_alert(kpi_id, value),
            'isPriority': kpi_def.get('is_priority', False),
            'order': kpi_def.get('display_order', 999),
            'category': kpi_def.get('category', 'general'),
            'unit': kpi_def.get('unit', ''),
            'frequency': kpi_def.get('frequency', 'daily')
        }
    
    def _calculate_kpi_value(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[float]:
        """Calcula o valor de um KPI."""
        # Verificar cache
        cached_value = self.cache.get(
            "kpi:value",
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        if cached_value is not None:
            return cached_value
        
        # Executar query
        value = None
        
        # Tentar query dinâmica
        dynamic_query = self.query_manager.get_kpi_query(kpi_id)
        if dynamic_query:
            value = self._execute_query(dynamic_query, timeframe, currency)
        
        # Fallback para queries hardcoded
        if value is None:
            value = self._execute_hardcoded_query(kpi_id, timeframe, currency)
        
        # Cachear resultado
        if value is not None:
            self.cache.set(
                "kpi:value",
                value,
                timeframe=timeframe,
                kpi_id=kpi_id,
                client_id=client_id,
                currency=currency
            )
        
        return value
    
    def _generate_chart_data(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """Gera dados para o gráfico do KPI."""
        # Verificar cache
        cached_data = self.cache.get(
            "kpi:chart",
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        if cached_data is not None:
            return cached_data
        
        # Gerar dados (implementação simplificada)
        chart_data = []
        
        # TODO: Implementar geração real de dados
        # Por enquanto, retornar dados mock
        import random
        for i in range(6):
            chart_data.append({
                'name': f'Ponto {i+1}',
                'value': random.uniform(100, 1000)
            })
        
        # Cachear resultado
        self.cache.set(
            "kpi:chart",
            chart_data,
            timeframe=timeframe,
            kpi_id=kpi_id,
            client_id=client_id,
            currency=currency
        )
        
        return chart_data
    
    def _get_timeframe_sql(self, timeframe: str) -> str:
        """Converte timeframe para filtro SQL."""
        mapping = {
            '1d': "data_operacao >= (SELECT MAX(data_operacao) FROM boleta)",
            'week': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '7 days' FROM boleta)",
            'month': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '30 days' FROM boleta)",
            'quarter': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '90 days' FROM boleta)"
        }
        return mapping.get(timeframe, mapping['week'])
    
    def _get_currency_sql(self, currency: str) -> str:
        """Converte currency para filtro SQL."""
        if currency == 'all':
            return "1=1"
        return f"UPPER(moeda) = '{currency.upper()}'"
    
    def invalidate_kpis(
        self,
        kpi_ids: Optional[List[str]] = None,
        client_id: Optional[str] = None,
        timeframe: Optional[str] = None,
        currency: Optional[str] = None
    ):
        """
        Invalida cache de KPIs específicos ou por padrão.
        
        Args:
            kpi_ids: Lista de IDs para invalidar (None = todos)
            client_id: Cliente específico (None = todos)
            timeframe: Timeframe específico (None = todos)
            currency: Moeda específica (None = todas)
        """
        # Invalidar dashboard cache
        if client_id:
            self.cache.invalidate("kpi:dashboard", client_id=client_id)
        else:
            self.cache.invalidate("kpi:dashboard")
        
        # Invalidar KPI values e charts
        if kpi_ids:
            for kpi_id in kpi_ids:
                if client_id and timeframe and currency:
                    # Invalidação específica
                    self.cache.invalidate(
                        "kpi:value",
                        kpi_id=kpi_id,
                        client_id=client_id,
                        timeframe=timeframe,
                        currency=currency
                    )
                    self.cache.invalidate(
                        "kpi:chart",
                        kpi_id=kpi_id,
                        client_id=client_id,
                        timeframe=timeframe,
                        currency=currency
                    )
                else:
                    # Invalidação por padrão
                    self.cache.invalidate("kpi:value", pattern=kpi_id)
                    self.cache.invalidate("kpi:chart", pattern=kpi_id)
        else:
            # Invalidar todos
            self.cache.invalidate("kpi:value")
            self.cache.invalidate("kpi:chart")
        
        logger.info(f"🗑️ Cache de KPIs invalidado")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache."""
        return self.cache.get_detailed_stats()
    
    # Métodos auxiliares (simplificados para exemplo)
    
    def _load_kpi_definitions(
        self,
        sector: str,
        category: Optional[str],
        priority_only: bool
    ) -> List[Dict[str, Any]]:
        """Carrega definições de KPIs."""
        # TODO: Implementar carregamento real
        # Por enquanto, retornar lista mockada
        return [
            {
                'id': 'total_volume',
                'name': 'Volume Total',
                'description': 'Volume total de operações',
                'format_type': 'currency',
                'is_priority': True,
                'display_order': 1,
                'category': 'volume'
            },
            {
                'id': 'average_spread',
                'name': 'Spread Médio',
                'description': 'Spread médio das operações',
                'format_type': 'percentage',
                'is_priority': True,
                'display_order': 2,
                'category': 'pricing'
            }
        ]
    
    def _execute_query(self, query: str, timeframe: str, currency: str) -> Optional[float]:
        """Executa query SQL com filtros."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            
            # Conectar ao banco
            db_config = load_db_config(setor="cambio", cliente="L2M")
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            
            # Substituir placeholders de filtros
            timeframe_sql = self._get_timeframe_sql(timeframe)
            currency_sql = self._get_currency_sql(currency)
            
            # Aplicar filtros na query
            query_with_filters = query
            if ':timeframe_filter' in query:
                query_with_filters = query_with_filters.replace(':timeframe_filter', timeframe_sql)
            if ':currency_filter' in query:
                query_with_filters = query_with_filters.replace(':currency_filter', currency_sql)
            
            # Executar query
            with engine.connect() as conn:
                result = conn.execute(text(query_with_filters))
                row = result.fetchone()
                
                if row and row[0] is not None:
                    return float(row[0])
                    
            return None
            
        except Exception as e:
            logger.error(f"❌ Error executing query: {e}")
            return None
    
    def _execute_hardcoded_query(self, kpi_id: str, timeframe: str, currency: str) -> Optional[float]:
        """Executa query hardcoded."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            
            # Conectar ao banco
            db_config = load_db_config(setor="cambio", cliente="L2M")
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            
            # Obter filtros SQL
            timeframe_sql = self._get_timeframe_sql(timeframe)
            currency_sql = self._get_currency_sql(currency)
            
            with engine.connect() as conn:
                if kpi_id == 'total_volume':
                    query = f'''
                        SELECT COALESCE(SUM(valor_me), 0) as total_volume
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND ({currency_sql})
                    '''
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    return float(row[0]) if row and row[0] else 0
                    
                elif kpi_id == 'average_spread':
                    query = f'''
                        SELECT COALESCE(AVG(ABS(taxa_operacao - taxa_mercado)), 0) as avg_spread
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND taxa_operacao IS NOT NULL
                        AND taxa_mercado IS NOT NULL
                        AND ({currency_sql})
                    '''
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    return float(row[0]) if row and row[0] else 0
                    
                elif kpi_id == 'average_ticket':
                    query = f'''
                        SELECT COALESCE(AVG(valor_me), 0) as avg_ticket
                        FROM boleta
                        WHERE ({timeframe_sql})
                        AND valor_me > 0
                        AND ({currency_sql})
                    '''
                    result = conn.execute(text(query))
                    row = result.fetchone()
                    return float(row[0]) if row and row[0] else 0
                    
                else:
                    # Fallback para outros KPIs
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error executing hardcoded query for {kpi_id}: {e}")
            return None
    
    def _calculate_change_percent(self, kpi_id: str, current_value: float, timeframe: str) -> float:
        """Calcula percentual de mudança."""
        # TODO: Implementar cálculo real
        import random
        return round(random.uniform(-10, 10), 2)
    
    def _determine_trend(self, kpi_id: str, value: float) -> str:
        """Determina tendência do KPI."""
        # TODO: Implementar análise real
        import random
        return random.choice(['up', 'down', 'stable'])
    
    def _check_alert(self, kpi_id: str, value: float) -> Optional[Dict[str, Any]]:
        """Verifica alertas para o KPI."""
        # TODO: Implementar verificação real
        return None


# Função helper para obter instância do serviço
def get_kpi_service_refactored() -> KpiCalculationServiceRefactored:
    """Retorna instância do serviço de KPIs refatorado."""
    return KpiCalculationServiceRefactored() 