"""KPI Calculator Service - Leverages existing SQL infrastructure."""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pathlib import Path

from src.agents.business_analyst import BusinessAnalystAgent
from src.nodes.execution_node import execution_node
from src.nodes.unified_validation_node import unified_validation_node  
from src.utils.component_manager import get_cached_schema
# KPIResponse will be imported dynamically to avoid circular imports

logger = logging.getLogger(__name__)

class KPICalculator:
    """Calculate KPIs using existing SQL execution infrastructure."""
    
    def __init__(self):
        """Initialize KPI calculator."""
        self.kpi_configs = {}
        self.business_analyst = None
        
    def _load_kpi_config(self, sector: str) -> Dict[str, Any]:
        """Load KPI configuration for sector."""
        if sector not in self.kpi_configs:
            config_path = Path(f"src/config/setores/{sector}/kpis-exchange-json.json")
            
            if not config_path.exists():
                raise FileNotFoundError(f"KPI configuration not found: {config_path}")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self.kpi_configs[sector] = json.load(f)
                
        return self.kpi_configs[sector]
    
    def _get_business_analyst(self, client_id: str, sector: str) -> BusinessAnalystAgent:
        """Get or create business analyst agent."""
        if self.business_analyst is None:
            # Use existing business analyst initialization
            self.business_analyst = BusinessAnalystAgent(
                setor=sector,
                cliente=client_id
            )
        return self.business_analyst
    
    def _build_kpi_sql_query(self, kpi_config: Dict[str, Any], period: str, client_id: str) -> str:
        """Build SQL query for KPI calculation based on configuration."""
        kpi_id = kpi_config["id"]
        formula = kpi_config["formula"]
        
        # Map period to date filters
        period_filters = self._get_period_filter(period)
        
        # KPI-specific SQL generation based on ID and formula
        if kpi_id == "total_volume":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                SUM(valor_me) as total_volume,
                COUNT(*) as total_operations,
                AVG(valor_me) as avg_volume
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "average_spread":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                AVG(
                    CASE 
                        WHEN tipo_operacao = 'VENDA' THEN 
                            ((taxa_cambio - taxa_base) / taxa_base) * 100
                        WHEN tipo_operacao = 'COMPRA' THEN 
                            ((taxa_base - taxa_cambio) / taxa_base) * 100
                        ELSE 0
                    END
                ) as average_spread,
                COUNT(*) as total_operations
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
              AND taxa_cambio IS NOT NULL 
              AND taxa_base IS NOT NULL
              AND taxa_base > 0
            """
            
        elif kpi_id == "average_ticket":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                AVG(valor_me) as average_ticket,
                SUM(valor_me) / COUNT(*) as calculated_avg,
                COUNT(*) as total_operations
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "volume_by_currency":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                bm.simbolo as codigo_moeda,
                SUM(b.valor_me) as volume_por_moeda,
                COUNT(*) as operacoes_por_moeda,
                ROUND(
                    (SUM(b.valor_me) * 100.0 / 
                     (SELECT SUM(valor_me) FROM boleta 
                      WHERE data_operacao {period_filters['date_filter']} AND id_cliente = {numeric_client_id})
                    ), 2
                ) as percentual_participacao
            FROM boleta b
            JOIN boleta_moeda bm ON b.id_moeda = bm.id
            WHERE b.data_operacao {period_filters['date_filter']}
              AND b.id_cliente = {numeric_client_id}
            GROUP BY bm.simbolo
            ORDER BY volume_por_moeda DESC
            LIMIT 10
            """
            
        elif kpi_id == "growth_percentage":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            current_filter = period_filters['date_filter']
            previous_filter = period_filters['previous_period_filter']
            
            return f"""
            WITH current_period AS (
                SELECT SUM(valor_me) as current_volume
                FROM boleta 
                WHERE data_operacao {current_filter}
                  AND id_cliente = {numeric_client_id}
            ),
            previous_period AS (
                SELECT SUM(valor_me) as previous_volume
                FROM boleta 
                WHERE data_operacao {previous_filter}
                  AND id_cliente = {numeric_client_id}
            )
            SELECT 
                current_volume,
                previous_volume,
                CASE 
                    WHEN previous_volume > 0 THEN
                        ROUND(((current_volume - previous_volume) / previous_volume) * 100, 2)
                    ELSE NULL
                END as growth_percentage
            FROM current_period, previous_period
            """
            
        elif kpi_id == "gross_margin":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                SUM(
                    valor_me * 
                    CASE 
                        WHEN tipo_operacao = 'VENDA' AND taxa_base > 0 THEN 
                            (taxa_cambio - taxa_base)
                        WHEN tipo_operacao = 'COMPRA' AND taxa_base > 0 THEN 
                            (taxa_base - taxa_cambio)
                        ELSE 0
                    END
                ) as gross_margin,
                COUNT(*) as total_operations,
                SUM(valor_me) as total_volume
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
              AND taxa_cambio IS NOT NULL 
              AND taxa_base IS NOT NULL
              AND taxa_base > 0
            """
            
        elif kpi_id == "net_margin":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                SUM(
                    valor_me * 
                    CASE 
                        WHEN tipo_operacao = 'VENDA' AND taxa_base > 0 THEN 
                            (taxa_cambio - taxa_base)
                        WHEN tipo_operacao = 'COMPRA' AND taxa_base > 0 THEN 
                            (taxa_base - taxa_cambio)
                        ELSE 0
                    END
                ) - SUM(
                    COALESCE(tarifa_bancaria, 0) + 
                    COALESCE(iof_cambio_valor, 0) + 
                    COALESCE(irrf_valor, 0) + 
                    COALESCE(iss_valor, 0) + 
                    COALESCE(pis_valor, 0) + 
                    COALESCE(cide_valor, 0) + 
                    COALESCE(cofins_valor, 0)
                ) as net_margin,
                COUNT(*) as total_operations,
                SUM(
                    COALESCE(tarifa_bancaria, 0) + 
                    COALESCE(iof_cambio_valor, 0) + 
                    COALESCE(irrf_valor, 0) + 
                    COALESCE(iss_valor, 0) + 
                    COALESCE(pis_valor, 0) + 
                    COALESCE(cide_valor, 0) + 
                    COALESCE(cofins_valor, 0)
                ) as total_costs
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
              AND taxa_cambio IS NOT NULL 
              AND taxa_base IS NOT NULL
              AND taxa_base > 0
            """
            
        elif kpi_id == "operations_roi":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH revenue_costs AS (
                SELECT 
                    SUM(
                        valor_me * 
                        CASE 
                            WHEN tipo_operacao = 'VENDA' AND taxa_base > 0 THEN 
                                (taxa_cambio - taxa_base)
                            WHEN tipo_operacao = 'COMPRA' AND taxa_base > 0 THEN 
                                (taxa_base - taxa_cambio)
                            ELSE 0
                        END
                    ) as total_revenue,
                    SUM(
                        COALESCE(tarifa_bancaria, 0) + 
                        COALESCE(iof_cambio_valor, 0) + 
                        COALESCE(irrf_valor, 0) + 
                        COALESCE(iss_valor, 0) + 
                        COALESCE(pis_valor, 0) + 
                        COALESCE(cide_valor, 0) + 
                        COALESCE(cofins_valor, 0)
                    ) as total_costs
                FROM boleta 
                WHERE data_operacao {period_filters['date_filter']}
                  AND id_cliente = {numeric_client_id}
                  AND taxa_cambio IS NOT NULL 
                  AND taxa_base IS NOT NULL
                  AND taxa_base > 0
            )
            SELECT 
                total_revenue,
                total_costs,
                CASE 
                    WHEN total_costs > 0 THEN
                        ROUND(((total_revenue - total_costs) / total_costs) * 100, 2)
                    ELSE NULL
                END as operations_roi
            FROM revenue_costs
            """
            
        elif kpi_id == "cost_per_operation":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                SUM(
                    COALESCE(tarifa_bancaria, 0) + 
                    COALESCE(iof_cambio_valor, 0) + 
                    COALESCE(irrf_valor, 0) + 
                    COALESCE(iss_valor, 0) + 
                    COALESCE(pis_valor, 0) + 
                    COALESCE(cide_valor, 0) + 
                    COALESCE(cofins_valor, 0)
                ) / COUNT(*) as cost_per_operation,
                COUNT(*) as total_operations,
                SUM(
                    COALESCE(tarifa_bancaria, 0) + 
                    COALESCE(iof_cambio_valor, 0) + 
                    COALESCE(irrf_valor, 0) + 
                    COALESCE(iss_valor, 0) + 
                    COALESCE(pis_valor, 0) + 
                    COALESCE(cide_valor, 0) + 
                    COALESCE(cofins_valor, 0)
                ) as total_costs
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "cost_to_income_ratio":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH costs_income AS (
                SELECT 
                    SUM(
                        COALESCE(tarifa_bancaria, 0) + 
                        COALESCE(iof_cambio_valor, 0) + 
                        COALESCE(irrf_valor, 0) + 
                        COALESCE(iss_valor, 0) + 
                        COALESCE(pis_valor, 0) + 
                        COALESCE(cide_valor, 0) + 
                        COALESCE(cofins_valor, 0)
                    ) as total_costs,
                    SUM(
                        valor_me * 
                        CASE 
                            WHEN tipo_operacao = 'VENDA' AND taxa_base > 0 THEN 
                                (taxa_cambio - taxa_base)
                            WHEN tipo_operacao = 'COMPRA' AND taxa_base > 0 THEN 
                                (taxa_base - taxa_cambio)
                            ELSE 0
                        END
                    ) as total_revenue
                FROM boleta 
                WHERE data_operacao {period_filters['date_filter']}
                  AND id_cliente = {numeric_client_id}
                  AND taxa_cambio IS NOT NULL 
                  AND taxa_base IS NOT NULL
                  AND taxa_base > 0
            )
            SELECT 
                total_costs,
                total_revenue,
                CASE 
                    WHEN total_revenue > 0 THEN
                        ROUND((total_costs / total_revenue) * 100, 2)
                    ELSE NULL
                END as cost_to_income_ratio
            FROM costs_income
            """
            
        elif kpi_id == "transaction_count":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                COUNT(*) as transaction_count,
                COUNT(CASE WHEN id_boleta_status = 3 THEN 1 END) as liquidated_count,
                COUNT(CASE WHEN id_boleta_status IN (2, 3) THEN 1 END) as approved_count,
                COUNT(CASE WHEN id_boleta_status IN (4, 5, 6) THEN 1 END) as rejected_count
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "processing_time":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                AVG(
                    CASE 
                        WHEN data_entrega_mn IS NOT NULL AND data_criacao IS NOT NULL THEN
                            EXTRACT(EPOCH FROM (data_entrega_mn - data_criacao)) / 86400.0
                        ELSE NULL
                    END
                ) as avg_processing_days,
                COUNT(
                    CASE 
                        WHEN data_entrega_mn IS NOT NULL AND data_criacao IS NOT NULL THEN 1
                        ELSE NULL
                    END
                ) as operations_with_processing_time,
                MIN(
                    CASE 
                        WHEN data_entrega_mn IS NOT NULL AND data_criacao IS NOT NULL THEN
                            EXTRACT(EPOCH FROM (data_entrega_mn - data_criacao)) / 86400.0
                        ELSE NULL
                    END
                ) as min_processing_days,
                MAX(
                    CASE 
                        WHEN data_entrega_mn IS NOT NULL AND data_criacao IS NOT NULL THEN
                            EXTRACT(EPOCH FROM (data_entrega_mn - data_criacao)) / 86400.0
                        ELSE NULL
                    END
                ) as max_processing_days
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "approval_rate":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                ROUND(
                    (COUNT(CASE WHEN id_boleta_status IN (2, 3) THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(*), 0)
                    ), 2
                ) as approval_rate,
                COUNT(*) as total_operations,
                COUNT(CASE WHEN id_boleta_status IN (2, 3) THEN 1 END) as approved_operations,
                COUNT(CASE WHEN id_boleta_status = 2 THEN 1 END) as concluded_operations,
                COUNT(CASE WHEN id_boleta_status = 3 THEN 1 END) as liquidated_operations
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "rejection_rate":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                ROUND(
                    (COUNT(CASE WHEN id_boleta_status IN (4, 5, 6) THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(*), 0)
                    ), 2
                ) as rejection_rate,
                COUNT(*) as total_operations,
                COUNT(CASE WHEN id_boleta_status IN (4, 5, 6) THEN 1 END) as rejected_operations,
                COUNT(CASE WHEN id_boleta_status = 4 THEN 1 END) as cancelled_operations,
                COUNT(CASE WHEN id_boleta_status = 5 THEN 1 END) as annulled_operations,
                COUNT(CASE WHEN id_boleta_status = 6 THEN 1 END) as removed_operations
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "average_processing_days":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                AVG(
                    CASE 
                        WHEN data_entrega_mn IS NOT NULL AND data_criacao IS NOT NULL THEN
                            EXTRACT(EPOCH FROM (data_entrega_mn - data_criacao)) / 86400.0
                        WHEN data_entrega_me IS NOT NULL AND data_criacao IS NOT NULL THEN
                            EXTRACT(EPOCH FROM (data_entrega_me - data_criacao)) / 86400.0
                        WHEN data_atualizacao IS NOT NULL AND data_criacao IS NOT NULL THEN
                            EXTRACT(EPOCH FROM (data_atualizacao - data_criacao)) / 86400.0
                        ELSE NULL
                    END
                ) as average_processing_days,
                COUNT(*) as total_operations,
                COUNT(
                    CASE 
                        WHEN data_entrega_mn IS NOT NULL AND data_criacao IS NOT NULL THEN 1
                        WHEN data_entrega_me IS NOT NULL AND data_criacao IS NOT NULL THEN 1
                        WHEN data_atualizacao IS NOT NULL AND data_criacao IS NOT NULL THEN 1
                        ELSE NULL
                    END
                ) as operations_with_dates
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "operational_efficiency":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                ROUND(
                    (COUNT(CASE WHEN id_boleta_status = 3 THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(*), 0)
                    ), 2
                ) as operational_efficiency,
                COUNT(*) as total_operations,
                COUNT(CASE WHEN id_boleta_status = 3 THEN 1 END) as liquidated_operations,
                AVG(
                    CASE 
                        WHEN data_entrega_mn IS NOT NULL AND data_criacao IS NOT NULL THEN
                            EXTRACT(EPOCH FROM (data_entrega_mn - data_criacao)) / 86400.0
                        ELSE NULL
                    END
                ) as avg_liquidation_time
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "throughput":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            SELECT 
                COUNT(*) as total_operations,
                SUM(valor_me) as total_volume_processed,
                COUNT(*)::numeric / NULLIF(
                    (MAX(data_operacao) - MIN(data_operacao)) + 1, 0
                ) as operations_per_day,
                SUM(valor_me) / NULLIF(
                    (MAX(data_operacao) - MIN(data_operacao)) + 1, 0
                ) as volume_per_day,
                MIN(data_operacao) as period_start,
                MAX(data_operacao) as period_end
            FROM boleta 
            WHERE data_operacao {period_filters['date_filter']}
              AND id_cliente = {numeric_client_id}
            """
            
        elif kpi_id == "capacity_utilization":
            # Map L2M to correct client ID
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH daily_operations AS (
                SELECT 
                    data_operacao::date as operation_date,
                    COUNT(*) as daily_operations,
                    SUM(valor_me) as daily_volume
                FROM boleta 
                WHERE data_operacao {period_filters['date_filter']}
                  AND id_cliente = {numeric_client_id}
                GROUP BY data_operacao::date
            ),
            capacity_stats AS (
                SELECT 
                    MAX(daily_operations) as max_daily_operations,
                    AVG(daily_operations) as avg_daily_operations,
                    MAX(daily_volume) as max_daily_volume,
                    AVG(daily_volume) as avg_daily_volume
                FROM daily_operations
            )
            SELECT 
                ROUND(
                    (avg_daily_operations / NULLIF(max_daily_operations, 0)) * 100, 2
                ) as capacity_utilization_operations,
                ROUND(
                    (avg_daily_volume / NULLIF(max_daily_volume, 0)) * 100, 2
                ) as capacity_utilization_volume,
                max_daily_operations,
                avg_daily_operations,
                max_daily_volume,
                avg_daily_volume
            FROM capacity_stats
            """
            
        elif kpi_id == "var":
            # Map L2M to correct client ID - Value at Risk calculation
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH daily_returns AS (
                SELECT 
                    data_operacao::date as operation_date,
                    SUM(
                        valor_me * 
                        CASE 
                            WHEN tipo_operacao = 'VENDA' AND taxa_base > 0 THEN 
                                (taxa_cambio - taxa_base) / taxa_base
                            WHEN tipo_operacao = 'COMPRA' AND taxa_base > 0 THEN 
                                (taxa_base - taxa_cambio) / taxa_base
                            ELSE 0
                        END
                    ) as daily_return,
                    SUM(valor_me) as daily_exposure
                FROM boleta 
                WHERE data_operacao {period_filters['date_filter']}
                  AND id_cliente = {numeric_client_id}
                  AND taxa_cambio IS NOT NULL 
                  AND taxa_base IS NOT NULL
                  AND taxa_base > 0
                GROUP BY data_operacao::date
            ),
            var_stats AS (
                SELECT 
                    AVG(daily_return) as mean_return,
                    STDDEV(daily_return) as return_volatility,
                    AVG(daily_exposure) as avg_exposure,
                    PERCENTILE_CONT(0.05) WITHIN GROUP (ORDER BY daily_return) as var_5_percent
                FROM daily_returns
            )
            SELECT 
                ABS(var_5_percent * avg_exposure) as var_amount,
                var_5_percent * 100 as var_percentage,
                return_volatility * 100 as daily_volatility_percent,
                avg_exposure,
                mean_return * 100 as avg_daily_return_percent
            FROM var_stats
            """
            
        elif kpi_id == "exposure_concentration":
            # Map L2M to correct client ID - Currency exposure concentration
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH currency_exposure AS (
                SELECT 
                    bm.simbolo as currency,
                    SUM(b.valor_me) as currency_volume,
                    COUNT(*) as currency_operations
                FROM boleta b
                JOIN boleta_moeda bm ON b.id_moeda = bm.id
                WHERE b.data_operacao {period_filters['date_filter']}
                  AND b.id_cliente = {numeric_client_id}
                GROUP BY bm.simbolo
            ),
            total_exposure AS (
                SELECT SUM(currency_volume) as total_volume
                FROM currency_exposure
            )
            SELECT 
                MAX(currency_volume / total_volume * 100) as max_currency_concentration,
                COUNT(*) as currency_count,
                SUM(POW(currency_volume / total_volume, 2)) as herfindahl_index,
                (1 - SUM(POW(currency_volume / total_volume, 2))) as diversification_index
            FROM currency_exposure, total_exposure
            """
            
        elif kpi_id == "currency_risk":
            # Map L2M to correct client ID - Currency risk assessment
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH currency_volatility AS (
                SELECT 
                    bm.simbolo as currency,
                    COUNT(*) as operations_count,
                    AVG(b.taxa_cambio) as avg_rate,
                    STDDEV(b.taxa_cambio) as rate_volatility,
                    (STDDEV(b.taxa_cambio) / NULLIF(AVG(b.taxa_cambio), 0)) * 100 as coefficient_variation,
                    SUM(b.valor_me) as total_exposure
                FROM boleta b
                JOIN boleta_moeda bm ON b.id_moeda = bm.id
                WHERE b.data_operacao {period_filters['date_filter']}
                  AND b.id_cliente = {numeric_client_id}
                  AND b.taxa_cambio IS NOT NULL
                GROUP BY bm.simbolo
            )
            SELECT 
                AVG(coefficient_variation) as avg_currency_volatility,
                MAX(coefficient_variation) as max_currency_volatility,
                SUM(total_exposure * coefficient_variation) / SUM(total_exposure) as weighted_volatility,
                COUNT(*) as currencies_traded,
                SUM(total_exposure) as total_currency_exposure
            FROM currency_volatility
            """
            
        elif kpi_id == "counterparty_risk":
            # Map L2M to correct client ID - Counterparty risk assessment via compliance
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH client_risk_profile AS (
                SELECT 
                    p.id_pessoa,
                    p.nome,
                    CASE 
                        WHEN pld.international_restrictive_lists = true THEN 100
                        WHEN pld.negative_media_sanctions = true THEN 80
                        WHEN pld.represented_by_attorney = true THEN 30
                        ELSE 10
                    END as risk_score,
                    COUNT(b.id_boleta) as operation_count,
                    SUM(b.valor_me) as total_exposure
                FROM pessoa p
                LEFT JOIN pld_compliance pld ON p.id_pessoa = pld.person_id
                LEFT JOIN boleta b ON p.id_pessoa = b.id_cliente 
                WHERE b.data_operacao {period_filters['date_filter']}
                  AND b.id_cliente = {numeric_client_id}
                GROUP BY p.id_pessoa, p.nome, pld.international_restrictive_lists, 
                         pld.negative_media_sanctions, pld.represented_by_attorney
            )
            SELECT 
                AVG(risk_score) as avg_counterparty_risk,
                MAX(risk_score) as max_counterparty_risk,
                SUM(total_exposure * risk_score) / NULLIF(SUM(total_exposure), 0) as weighted_risk_score,
                COUNT(*) as counterparties_count,
                SUM(total_exposure) as total_counterparty_exposure
            FROM client_risk_profile
            """
            
        elif kpi_id == "compliance_score":
            # Map L2M to correct client ID - Compliance score calculation
            numeric_client_id = 334 if client_id == 'L2M' else client_id
            return f"""
            WITH client_compliance AS (
                SELECT 
                    p.id_pessoa,
                    p.nome,
                    CASE 
                        WHEN pld.international_restrictive_lists = false 
                             AND pld.negative_media_sanctions = false THEN 100
                        WHEN pld.international_restrictive_lists = false 
                             AND pld.negative_media_sanctions = true THEN 70
                        WHEN pld.international_restrictive_lists = true 
                             AND pld.negative_media_sanctions = false THEN 50
                        ELSE 20
                    END as compliance_score,
                    COUNT(b.id_boleta) as recent_operations
                FROM pessoa p
                LEFT JOIN pld_compliance pld ON p.id_pessoa = pld.person_id
                LEFT JOIN boleta b ON p.id_pessoa = b.id_cliente 
                    AND b.data_operacao {period_filters['date_filter']}
                WHERE p.id_pessoa = {numeric_client_id}
                GROUP BY p.id_pessoa, p.nome, pld.international_restrictive_lists, 
                         pld.negative_media_sanctions
            ),
            system_compliance AS (
                SELECT 
                    AVG(
                        CASE 
                            WHEN tcs.status = 'LOW' THEN 90
                            WHEN tcs.status = 'MEDIUM' THEN 70
                            WHEN tcs.status = 'HIGH' THEN 40
                            WHEN tcs.status = 'CRITICAL' THEN 20
                            ELSE 75
                        END
                    ) as avg_system_compliance,
                    COUNT(*) as compliance_records
                FROM tb_compliance_score tcs
            )
            SELECT
                cc.compliance_score as client_compliance_score,
                sc.avg_system_compliance as system_compliance_score,
                ROUND(
                    (cc.compliance_score + sc.avg_system_compliance) / 2, 2
                ) as overall_compliance_score,
                cc.recent_operations,
                sc.compliance_records as system_compliance_records
            FROM client_compliance cc
            CROSS JOIN system_compliance sc
            WHERE cc.compliance_score IS NOT NULL
            """
            
        else:
            # Fail fast para KPIs desconhecidos
            raise ValueError(f"No SQL query implementation found for KPI: {kpi_id}")
    
    def _get_period_filter(self, period: str) -> Dict[str, str]:
        """Get SQL date filters for the specified period."""
        now = datetime.now()
        
        if period == "current_month":
            start_date = now.replace(day=1)
            date_filter = f">= '{start_date.strftime('%Y-%m-%d')}'"
            
            # Previous month for comparison
            if start_date.month == 1:
                prev_start = start_date.replace(year=start_date.year-1, month=12)
                prev_end = start_date - timedelta(days=1)
            else:
                prev_start = start_date.replace(month=start_date.month-1)
                prev_end = start_date - timedelta(days=1)
                
            previous_filter = f"BETWEEN '{prev_start.strftime('%Y-%m-%d')}' AND '{prev_end.strftime('%Y-%m-%d')}'"
            
        elif period == "last_month":
            if now.month == 1:
                start_date = now.replace(year=now.year-1, month=12, day=1)
                end_date = now.replace(day=1) - timedelta(days=1)
            else:
                start_date = now.replace(month=now.month-1, day=1)
                if now.month == 1:
                    end_date = now.replace(day=1) - timedelta(days=1)
                else:
                    end_date = now.replace(day=1) - timedelta(days=1)
                    
            date_filter = f"BETWEEN '{start_date.strftime('%Y-%m-%d')}' AND '{end_date.strftime('%Y-%m-%d')}'"
            
            # Previous month for comparison  
            if start_date.month == 1:
                prev_start = start_date.replace(year=start_date.year-1, month=12)
                prev_end = start_date - timedelta(days=1)
            else:
                prev_start = start_date.replace(month=start_date.month-1)
                prev_end = start_date - timedelta(days=1)
                
            previous_filter = f"BETWEEN '{prev_start.strftime('%Y-%m-%d')}' AND '{prev_end.strftime('%Y-%m-%d')}'"
            
        elif period == "ytd":
            start_date = now.replace(month=1, day=1)
            date_filter = f">= '{start_date.strftime('%Y-%m-%d')}'"
            
            # Previous year same period
            prev_start = start_date.replace(year=start_date.year-1)
            prev_end = now.replace(year=now.year-1)
            previous_filter = f"BETWEEN '{prev_start.strftime('%Y-%m-%d')}' AND '{prev_end.strftime('%Y-%m-%d')}'"
            
        elif period == "last_7_days":
            start_date = now - timedelta(days=7)
            date_filter = f">= '{start_date.strftime('%Y-%m-%d')}'"
            
            # Previous 7 days for comparison
            prev_start = start_date - timedelta(days=7)
            prev_end = start_date - timedelta(days=1)
            previous_filter = f"BETWEEN '{prev_start.strftime('%Y-%m-%d')}' AND '{prev_end.strftime('%Y-%m-%d')}'"
            
        else:
            # Default to current month
            start_date = now.replace(day=1)
            date_filter = f">= '{start_date.strftime('%Y-%m-%d')}'"
            previous_filter = f"< '{start_date.strftime('%Y-%m-%d')}'"
        
        return {
            "date_filter": date_filter,
            "previous_period_filter": previous_filter
        }
    
    def _execute_kpi_sql(self, sql_query: str, client_id: str, sector: str) -> List[Dict[str, Any]]:
        """Execute KPI SQL query using existing execution infrastructure."""
        # Build minimal state for SQL execution
        state = {
            "sql_query": sql_query,
            "client_id": client_id,
            "sector": sector,
            "schema_relevance_path": f"src/config/setores/{sector}/{client_id}/{client_id}_schema_relevance.json",
            "query_valid": True,  # Skip validation for KPI queries
            "validation_errors": [],
            "attempts": 0
        }
        
        try:
            # Use existing execution node
            result_state = execution_node(state)
            
            if result_state.get("error"):
                logger.error(f"KPI SQL execution error: {result_state['error']}")
                return []
                
            results = result_state.get("query_result", [])
            logger.info(f"KPI SQL executed successfully: {len(results)} rows")
            return results
            
        except Exception as e:
            logger.error(f"Error executing KPI SQL: {e}")
            return []
    
    def _generate_chart_data(self, kpi_id: str, sql_results: List[Dict[str, Any]], kpi_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate chart data for KPI visualization."""
        if not sql_results:
            return {
                "type": "line",
                "data": []
            }
            
        chart_type = "line"  # Default
        chart_data = []
        
        # KPI-specific chart generation
        if kpi_id == "volume_by_currency":
            chart_type = "bar"
            for row in sql_results:
                chart_data.append({
                    "name": row.get("codigo_moeda", "Unknown"),
                    "value": float(row.get("volume_por_moeda", 0)),
                    "percentage": float(row.get("percentual_participacao", 0))
                })
                
        elif kpi_id in ["total_volume", "average_ticket", "average_spread"]:
            chart_type = "area"
            # Get historical data for the last 30 days
            # Default to L2M client ID (334) if not in results
            client_id = 334  # L2M default
            historical_data = self._get_historical_data(kpi_id, client_id)
            
            if historical_data:
                chart_data = historical_data
            else:
                # Fail fast - não gerar dados mock
                raise ValueError(f"No historical data available for KPI {kpi_id}")
                    
        elif kpi_id == "growth_percentage":
            chart_type = "gauge"
            row = sql_results[0]
            current = float(row.get("current_volume", 0))
            previous = float(row.get("previous_volume", 0))
            growth = float(row.get("growth_percentage", 0))
            
            chart_data = [{
                "name": "Current",
                "value": current,
                "previous": previous,
                "growth": growth
            }]
        else:
            # Fail fast para outros KPIs sem implementação específica
            raise ValueError(f"Chart data generation not implemented for KPI {kpi_id}")
        
        return {
            "type": chart_type,
            "data": chart_data
        }
    
    def _get_historical_data(self, kpi_id: str, client_id: int) -> List[Dict[str, Any]]:
        """Get historical data for charts."""
        try:
            if kpi_id == "total_volume":
                # Query for daily volume over the last 30 days
                sql_query = f"""
                SELECT 
                    DATE(data_operacao) as data,
                    SUM(valor_me) as daily_volume
                FROM boleta 
                WHERE valor_me IS NOT NULL
                  AND valor_me > 0
                  AND id_cliente = {client_id}
                  AND data_operacao >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY DATE(data_operacao)
                ORDER BY data DESC
                LIMIT 30
                """
            elif kpi_id == "average_ticket":
                sql_query = f"""
                SELECT 
                    DATE(data_operacao) as data,
                    AVG(valor_me) as daily_avg_ticket
                FROM boleta 
                WHERE valor_me IS NOT NULL
                  AND valor_me > 0
                  AND id_cliente = {client_id}
                  AND data_operacao >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY DATE(data_operacao)
                ORDER BY data DESC
                LIMIT 30
                """
            elif kpi_id == "average_spread":
                sql_query = f"""
                SELECT 
                    DATE(data_operacao) as data,
                    AVG(
                        CASE 
                            WHEN tipo_operacao = 'VENDA' THEN 
                                ((taxa_cambio - taxa_base) / taxa_base) * 100
                            WHEN tipo_operacao = 'COMPRA' THEN 
                                ((taxa_base - taxa_cambio) / taxa_base) * 100
                            ELSE 0
                        END
                    ) as daily_avg_spread
                FROM boleta 
                WHERE taxa_cambio IS NOT NULL 
                  AND taxa_base IS NOT NULL
                  AND taxa_base > 0
                  AND id_cliente = {client_id}
                  AND data_operacao >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY DATE(data_operacao)
                ORDER BY data DESC
                LIMIT 30
                """
            else:
                return []
            
            # Execute query
            results = self._execute_kpi_sql(sql_query, str(client_id), "cambio")
            
            # Format results for chart
            chart_data = []
            for row in results:
                date = row.get("data")
                value_key = "daily_volume" if kpi_id == "total_volume" else \
                           "daily_avg_ticket" if kpi_id == "average_ticket" else \
                           "daily_avg_spread"
                value = row.get(value_key, 0)
                
                if date and value is not None:
                    chart_data.append({
                        "name": f"Dia {date.day}",
                        "value": float(value),
                        "date": date.strftime("%Y-%m-%d") if hasattr(date, 'strftime') else str(date)
                    })
            
            # Fail fast se não há dados históricos
            if not chart_data:
                raise ValueError(f"No historical data found for KPI {kpi_id}")

            return chart_data

        except Exception as e:
            logger.error(f"Error getting historical data for {kpi_id}: {e}")
            # Fail fast - não gerar dados fallback
            raise
    

    
    def _calculate_change_metrics(self, current_value: Any, kpi_id: str, sql_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate change percentage and direction for KPI."""
        change_data = {
            "change_percentage": None,
            "change_direction": "stable"
        }
        
        try:
            if kpi_id == "growth_percentage" and sql_results:
                # Growth percentage KPI already contains the change
                growth = sql_results[0].get("growth_percentage")
                if growth is not None:
                    change_data["change_percentage"] = float(growth)
                    change_data["change_direction"] = "up" if growth > 0 else "down" if growth < 0 else "stable"
            else:
                # For other KPIs, we would need historical comparison
                # For now, generate sample change (in production, compare with previous period)
                import random
                sample_change = random.uniform(-15, 25)  # Sample change between -15% and +25%
                change_data["change_percentage"] = round(sample_change, 1)
                change_data["change_direction"] = "up" if sample_change > 2 else "down" if sample_change < -2 else "stable"
                
        except Exception as e:
            logger.warning(f"Error calculating change metrics for {kpi_id}: {e}")
            
        return change_data
    
    def _format_kpi_value(self, value: Any, unit: str, kpi_config: Dict[str, Any]) -> str:
        """Format KPI value according to its unit and type."""
        if value is None:
            return "N/A"
            
        try:
            if unit == "Valor monetário (R$, US$, etc.)":
                if isinstance(value, (int, float)):
                    # Format as currency
                    if value >= 1000000:
                        return f"${value/1000000:.1f}M"
                    elif value >= 1000:
                        return f"${value/1000:.1f}K"
                    else:
                        return f"${value:,.0f}"
                        
            elif unit == "Percentual (%)":
                if isinstance(value, (int, float)):
                    return f"{value:.1f}%"
                    
            elif unit in ["Horas/Dias", "Operações/Analista/Dia"]:
                if isinstance(value, (int, float)):
                    return f"{value:.1f}"
                    
            else:
                # Generic formatting
                if isinstance(value, (int, float)):
                    return f"{value:,.0f}"
                else:
                    return str(value)
                    
        except Exception as e:
            logger.warning(f"Error formatting value {value} with unit {unit}: {e}")
            return str(value)
        
        return str(value)
    
    def _check_kpi_alerts(self, kpi_id: str, current_value: Any, kpi_config: Dict[str, Any]) -> Dict[str, Any]:
        """Check if KPI value triggers any alerts."""
        alert_info = {
            "alert_status": "normal",
            "alert_message": None
        }
        
        try:
            # Sample alert logic (in production, load from alert configuration)
            if kpi_id == "total_volume" and isinstance(current_value, (int, float)):
                if current_value < 1000000:  # Less than 1M
                    alert_info["alert_status"] = "warning"
                    alert_info["alert_message"] = "Volume below expected threshold"
                elif current_value > 10000000:  # More than 10M
                    alert_info["alert_status"] = "info"
                    alert_info["alert_message"] = "Exceptional volume period"
                    
            elif kpi_id == "average_spread" and isinstance(current_value, (int, float)):
                if current_value < 0.1:  # Less than 0.1%
                    alert_info["alert_status"] = "critical" 
                    alert_info["alert_message"] = "Spread critically low - review pricing"
                elif current_value > 2.0:  # More than 2%
                    alert_info["alert_status"] = "warning"
                    alert_info["alert_message"] = "High spread - competitive risk"
                    
        except Exception as e:
            logger.warning(f"Error checking alerts for {kpi_id}: {e}")
            
        return alert_info
    
    async def calculate_kpi(self, kpi_id: str, client_id: str, sector: str, period: str = "current_month", include_chart_data: bool = True):
        """Calculate a specific KPI."""
        try:
            logger.info(f"📊 Calculating KPI {kpi_id} for {client_id}/{sector}")
            
            # Load KPI configuration
            kpi_config_data = self._load_kpi_config(sector)
            
            # Find the KPI in configuration
            kpi_config = None
            for category in kpi_config_data.get("categories", []):
                for kpi in category.get("kpis", []):
                    if kpi["id"] == kpi_id:
                        kpi_config = kpi
                        break
                if kpi_config:
                    break
                    
            if not kpi_config:
                logger.error(f"KPI {kpi_id} not found in configuration")
                return None
            
            # Generate SQL query
            sql_query = self._build_kpi_sql_query(kpi_config, period, client_id)
            logger.info(f"📊 Generated SQL for {kpi_id}: {sql_query[:100]}...")
            
            # Execute SQL query
            sql_results = self._execute_kpi_sql(sql_query, client_id, sector)
            
            if not sql_results:
                logger.warning(f"No data returned for KPI {kpi_id}")
                return None
            
            # Extract main value from results
            result_row = sql_results[0]
            current_value = None
            
            # Try to find the main value in the result
            for key, value in result_row.items():
                if isinstance(value, (int, float)) and value is not None:
                    current_value = value
                    break
            
            if current_value is None:
                logger.warning(f"Could not extract value for KPI {kpi_id}")
                return None
            
            # Format the value
            formatted_value = self._format_kpi_value(current_value, kpi_config["unit"], kpi_config)
            
            # Calculate change metrics
            change_metrics = self._calculate_change_metrics(current_value, kpi_id, sql_results)
            
            # Generate chart data if requested
            chart_data = None
            chart_type = None
            if include_chart_data:
                chart_info = self._generate_chart_data(kpi_id, sql_results, kpi_config)
                if chart_info:
                    chart_data = chart_info["data"]
                    chart_type = chart_info["type"]
            
            # Check for alerts
            alert_info = self._check_kpi_alerts(kpi_id, current_value, kpi_config)
            
            # Build KPI response (import here to avoid circular imports)
            from src.interfaces.dashboard_api import KPIResponse
            kpi_response = KPIResponse(
                kpi_id=kpi_id,
                name=kpi_config["name"],
                description=kpi_config["description"],
                current_value=current_value,
                formatted_value=formatted_value,
                unit=kpi_config["unit"],
                change_percentage=change_metrics["change_percentage"],
                change_direction=change_metrics["change_direction"],
                chart_data=chart_data,
                chart_type=chart_type,
                alert_status=alert_info["alert_status"],
                alert_message=alert_info["alert_message"],
                last_updated=datetime.now(),
                metadata={
                    "formula": kpi_config["formula"],
                    "frequency": kpi_config["frequency"],
                    "importance": kpi_config["importance"],
                    "sql_query": sql_query,
                    "result_count": len(sql_results),
                    "period": period
                }
            )
            
            logger.info(f"📊 KPI {kpi_id} calculated: {formatted_value}")
            return kpi_response
            
        except Exception as e:
            logger.error(f"❌ Error calculating KPI {kpi_id}: {e}")
            return None
    
    async def calculate_all_kpis(self, client_id: str, sector: str, period: str = "current_month", category: Optional[str] = None, priority_only: bool = False):
        """Calculate all KPIs for a client/sector."""
        try:
            logger.info(f"📊 Calculating all KPIs for {client_id}/{sector}")
            
            # Load KPI configuration
            kpi_config_data = self._load_kpi_config(sector)
            
            kpis = []
            total_kpis = 0
            
            for cat in kpi_config_data.get("categories", []):
                # Skip category if filter specified
                if category and cat["id"] != category:
                    continue
                    
                for kpi_config in cat.get("kpis", []):
                    total_kpis += 1
                    
                    # Skip if priority_only and this KPI is not priority
                    # (In production, you'd have priority flags in config)
                    if priority_only and kpi_config["id"] not in ["total_volume", "average_spread", "average_ticket"]:
                        continue
                    
                    # Calculate individual KPI
                    kpi_response = await self.calculate_kpi(
                        kpi_id=kpi_config["id"],
                        client_id=client_id,
                        sector=sector,
                        period=period,
                        include_chart_data=True
                    )
                    
                    if kpi_response:
                        kpis.append(kpi_response)
            
            logger.info(f"📊 Calculated {len(kpis)}/{total_kpis} KPIs successfully")
            return kpis
            
        except Exception as e:
            logger.error(f"❌ Error calculating all KPIs: {e}")
            return []
    
    async def get_kpi_history(self, kpi_id: str, client_id: str, sector: str, days: int = 30, granularity: str = "daily") -> List[Dict[str, Any]]:
        """Get historical data for a KPI."""
        try:
            logger.info(f"📊 Getting KPI history for {kpi_id} - {days} days")
            
            # Generate historical data points
            history = []
            now = datetime.now()
            
            for i in range(days):
                date = now - timedelta(days=days - i - 1)
                
                # In production, you'd calculate actual historical values
                # For now, generate sample data based on the date
                base_value = 1000000 + (i * 50000)  # Sample trending data
                daily_variation = (date.day % 7 - 3) * 0.05  # Sample daily variation
                value = base_value * (1 + daily_variation)
                
                history.append({
                    "date": date.strftime("%Y-%m-%d"),
                    "value": round(value, 2),
                    "formatted_date": date.strftime("%b %d")
                })
            
            return history
            
        except Exception as e:
            logger.error(f"❌ Error getting KPI history: {e}")
            return []