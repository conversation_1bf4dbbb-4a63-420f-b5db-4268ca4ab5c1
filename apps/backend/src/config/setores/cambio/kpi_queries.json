{"total_volume": "\n            SELECT COALESCE(SUM(valor_me), 0) as total_volume\n            FROM boleta\n            WHERE valor_me IS NOT NULL\n            AND valor_me > 0\n            AND (:timeframe_filter)\n            AND (:currency_filter)\n        ", "metadata": {"total_volume": {"generated_at": "2025-07-10T22:30:00.000000", "generated_by": "query_builder", "note": "Removido filtro de cliente - período alterado para 1 mês"}, "average_ticket": {"generated_at": "2025-07-10T22:30:00.000000", "generated_by": "query_builder", "note": "Removido filtro de cliente - período alterado para 1 mês"}, "average_spread": {"generated_at": "2025-07-10T22:30:00.000000", "generated_by": "query_builder", "note": "Removido filtro de cliente - período alterado para 1 mês"}, "conversion_rate": {"generated_at": "2025-07-10T22:30:00.000000", "generated_by": "query_builder", "note": "Removido filtro de cliente - período alterado para 1 mês"}, "retention_rate": {"generated_at": "2025-07-10T22:30:00.000000", "generated_by": "query_builder", "note": "Man<PERSON><PERSON> período de 6 meses para comparação de retenção"}, "operations_per_analyst": {"generated_at": "2025-07-10T22:30:00.000000", "generated_by": "query_builder", "note": "Já estava correto - 30 dias"}}, "average_ticket": "\n            SELECT AVG(valor_me) as average_ticket\n            FROM boleta\n            WHERE valor_me IS NOT NULL\n            AND valor_me > 0\n            AND (:timeframe_filter)\n            AND (:currency_filter)\n        ", "average_spread": "\n            SELECT AVG(\n                CASE\n                    WHEN taxa_base > 0 THEN\n                        ABS((taxa_cambio - taxa_base) / taxa_base) * 100\n                    ELSE 0\n                END\n            ) as average_spread\n            FROM boleta\n            WHERE taxa_cambio IS NOT NULL\n            AND taxa_base IS NOT NULL\n            AND taxa_base > 0\n            AND (:timeframe_filter)\n            AND (:currency_filter)\n        ", "conversion_rate": "\n            SELECT\n                CASE\n                    WHEN COUNT(*) > 0 THEN\n                        ROUND(\n                            COUNT(CASE WHEN id_boleta_status IN (4, 7) THEN 1 END) * 100.0 / COUNT(*),\n                            2\n                        )\n                    ELSE 0\n                END as conversion_rate\n            FROM boleta\n            WHERE (:timeframe_filter)\n            AND (:currency_filter)\n        ", "retention_rate": "\n            WITH all_periods AS (\n                SELECT \n                    id_cliente,\n                    DATE_TRUNC('year', data_operacao) as year\n                FROM boleta\n                WHERE id_cliente IS NOT NULL\n                GROUP BY id_cliente, DATE_TRUNC('year', data_operacao)\n            ),\n            client_years AS (\n                SELECT \n                    id_cliente,\n                    COUNT(DISTINCT year) as years_active\n                FROM all_periods\n                GROUP BY id_cliente\n            )\n            SELECT\n                ROUND(\n                    COUNT(CASE WHEN years_active > 1 THEN 1 END) * 100.0 / \n                    NULLIF(COUNT(*), 0),\n                    2\n                ) as retention_rate\n            FROM client_years\n        ", "operations_per_analyst": "\n            SELECT \n                ROUND(\n                    COUNT(*) * 1.0 / \n                    GREATEST(\n                        COUNT(DISTINCT id_funcionario_criador),\n                        1\n                    ),\n                    2\n                ) as operations_per_analyst\n            FROM boleta\n            WHERE data_criacao >= CURRENT_DATE - INTERVAL '30 days'\n            AND id_funcionario_criador IS NOT NULL\n        ", "volume_by_currency": "\n            SELECT \n                id_moeda as currency,\n                COALESCE(SUM(valor_me), 0) as volume\n            FROM boleta\n            WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            AND valor_me IS NOT NULL\n            GROUP BY id_moeda\n            ORDER BY volume DESC\n        ", "growth_percentage": "\n            WITH current_month AS (\n                SELECT COALESCE(SUM(valor_me), 0) as volume\n                FROM boleta\n                WHERE data_operacao >= DATE_TRUNC('month', CURRENT_DATE)\n                AND data_operacao < DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month'\n            ),\n            previous_month AS (\n                SELECT COALESCE(SUM(valor_me), 0) as volume\n                FROM boleta\n                WHERE data_operacao >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'\n                AND data_operacao < DATE_TRUNC('month', CURRENT_DATE)\n            )\n            SELECT\n                CASE\n                    WHEN (SELECT volume FROM previous_month) > 0 THEN\n                        ROUND(\n                            ((SELECT volume FROM current_month) - (SELECT volume FROM previous_month)) * 100.0 / \n                            (SELECT volume FROM previous_month),\n                            2\n                        )\n                    ELSE 0\n                END as growth_percentage\n        ", "average_settlement_time": "\n            SELECT \n                AVG(\n                    EXTRACT(EPOCH FROM (data_liquidacao - data_operacao)) / 86400\n                ) as avg_settlement_days\n            FROM boleta\n            WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            AND data_liquidacao IS NOT NULL\n            AND data_operacao IS NOT NULL\n        ", "automation_rate": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as automation_rate\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "gross_margin": "\n            SELECT COALESCE(SUM(\n                valor_me * \n                CASE \n                    WHEN tipo_operacao = 'VENDA' AND taxa_base > 0 THEN \n                        (taxa_cambio - taxa_base) / taxa_base\n                    WHEN tipo_operacao = 'COMPRA' AND taxa_base > 0 THEN \n                        (taxa_base - taxa_cambio) / taxa_base\n                    ELSE 0\n                END\n            ), 0) as gross_margin\n            FROM boleta\n            WHERE taxa_cambio IS NOT NULL \n            AND taxa_base IS NOT NULL\n            AND taxa_base > 0\n            AND valor_me IS NOT NULL\n            AND (:timeframe_filter)\n            AND (:currency_filter)\n        ", "net_margin": "\n                SELECT COALESCE(SUM(valor_me), 0) as net_margin\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND valor_me IS NOT NULL\n            ", "operations_roi": "\n                SELECT COALESCE(COUNT(*), 0) as operations_roi\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "cost_per_operation": "\n                SELECT COALESCE(COUNT(*), 0) as cost_per_operation\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "cost_to_income_ratio": "\n                SELECT COALESCE(COUNT(*), 0) as cost_to_income_ratio\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "churn_rate": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as churn_rate\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "ltv": "\n                SELECT COALESCE(COUNT(*), 0) as ltv\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "client_concentration": "\n                SELECT COALESCE(COUNT(*), 0) as client_concentration\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "currency_exposure": "\n                SELECT COALESCE(SUM(valor_me), 0) as currency_exposure\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND valor_me IS NOT NULL\n            ", "rate_volatility": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as rate_volatility\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "limit_utilization": "\n                SELECT COALESCE(COUNT(*), 0) as limit_utilization\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "var": "\n                SELECT COALESCE(COUNT(*), 0) as var\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "reported_operations_rate": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as reported_operations_rate\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "compliance_analysis_time": "\n                SELECT COALESCE(COUNT(*), 0) as compliance_analysis_time\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "compliance_rejection_rate": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as compliance_rejection_rate\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "fraud_rate": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as fraud_rate\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "volume_per_operator": "\n                SELECT COALESCE(COUNT(valor_me), 0) as volume_per_operator\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND valor_me IS NOT NULL\n            ", "average_spread_by_operator": "\n                SELECT COALESCE(COUNT(ABS(taxa_cambio - taxa_base)), 0) as average_spread_by_operator\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND ABS(taxa_cambio - taxa_base) IS NOT NULL\n            ", "sla_rate_by_team": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as sla_rate_by_team\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "talent_retention_rate": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as talent_retention_rate\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "yoy_growth_rate": "\n                SELECT COALESCE(COUNT(taxa_cambio), 0) as yoy_growth_rate\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND taxa_cambio IS NOT NULL\n            ", "diversification_index": "\n                SELECT COALESCE(COUNT(*), 0) as diversification_index\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            ", "ebitda_volume_ratio": "\n                SELECT COALESCE(COUNT(valor_me), 0) as ebitda_volume_ratio\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n                AND valor_me IS NOT NULL\n            ", "ltv_cac_ratio": "\n                SELECT COALESCE(COUNT(*), 0) as ltv_cac_ratio\n                FROM boleta\n                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'\n            "}