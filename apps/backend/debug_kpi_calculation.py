#!/usr/bin/env python3
"""
Debug específico para identificar onde está o gargalo no cálculo de KPIs.
"""

import os
import sys
import time
import asyncio
from pathlib import Path

# Adicionar o diretório raiz ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Carregar variáveis de ambiente
from dotenv import load_dotenv
load_dotenv()

def test_individual_kpi_calculation():
    """Testa o cálculo individual de cada KPI para identificar gargalos."""
    print("🔍 TESTANDO CÁLCULO INDIVIDUAL DE KPIS")
    print("=" * 50)
    
    try:
        from src.services.kpi_service_refactored import get_kpi_service_refactored
        
        kpi_service = get_kpi_service_refactored()
        
        # KPIs críticos para testar
        critical_kpis = [
            'total_volume',
            'average_ticket', 
            'average_spread',
            'conversion_rate',
            'retention_rate',
            'gross_margin'
        ]
        
        print(f"📊 Testando {len(critical_kpis)} KPIs críticos...")
        
        total_time = 0
        results = []
        
        for i, kpi_id in enumerate(critical_kpis, 1):
            print(f"\n🔸 [{i}/{len(critical_kpis)}] Testando KPI: {kpi_id}")
            print("-" * 40)
            
            # Limpar cache para este KPI específico
            try:
                kpi_service.invalidate_kpis([kpi_id], "L2M", "week", "all")
                print("🗑️  Cache limpo para este KPI")
            except:
                print("⚠️  Não foi possível limpar cache")
            
            # Calcular KPI individual
            start_time = time.time()
            
            try:
                kpi_data = kpi_service._calculate_single_kpi(
                    kpi_def={"id": kpi_id, "title": f"Test {kpi_id}"},
                    client_id="L2M",
                    timeframe="week",
                    currency="all"
                )
                
                calc_time = (time.time() - start_time) * 1000
                total_time += calc_time
                
                if kpi_data:
                    print(f"✅ Sucesso: {calc_time:.0f}ms")
                    print(f"   Valor: {kpi_data.get('currentValue', 'N/A')}")
                    print(f"   Trend: {kpi_data.get('trend', 'N/A')}")
                    print(f"   Chart data: {len(kpi_data.get('chartData', []))} pontos")
                else:
                    print(f"❌ Falhou: {calc_time:.0f}ms")
                
                results.append({
                    "kpi_id": kpi_id,
                    "time_ms": calc_time,
                    "success": kpi_data is not None,
                    "value": kpi_data.get('currentValue') if kpi_data else None
                })
                
            except Exception as e:
                calc_time = (time.time() - start_time) * 1000
                total_time += calc_time
                print(f"❌ Erro: {calc_time:.0f}ms - {e}")
                
                results.append({
                    "kpi_id": kpi_id,
                    "time_ms": calc_time,
                    "success": False,
                    "error": str(e)
                })
            
            # Pausa entre KPIs
            time.sleep(1)
        
        # Resumo
        print(f"\n{'='*50}")
        print("📋 RESUMO DO TESTE INDIVIDUAL")
        print(f"{'='*50}")
        
        print(f"⏱️  Tempo total: {total_time:.0f}ms ({total_time/1000:.1f}s)")
        print(f"⏱️  Tempo médio por KPI: {total_time/len(critical_kpis):.0f}ms")
        
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]
        
        print(f"✅ Sucessos: {len(successful)}/{len(results)}")
        print(f"❌ Falhas: {len(failed)}/{len(results)}")
        
        if successful:
            avg_success_time = sum(r["time_ms"] for r in successful) / len(successful)
            print(f"⏱️  Tempo médio (sucessos): {avg_success_time:.0f}ms")
        
        # KPIs mais lentos
        slow_kpis = [r for r in results if r["time_ms"] > 5000]
        if slow_kpis:
            print(f"\n⚠️  KPIs lentos (>5s):")
            for kpi in slow_kpis:
                print(f"   - {kpi['kpi_id']}: {kpi['time_ms']:.0f}ms")
        
        return results
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return []


def test_database_connection_speed():
    """Testa a velocidade da conexão com o banco de dados."""
    print("\n🗄️  TESTANDO VELOCIDADE DO BANCO DE DADOS")
    print("=" * 45)
    
    try:
        from src.tools.db_utils import load_db_config, build_connection_string, get_engine
        from sqlalchemy import text
        
        # Conectar ao banco
        print("🔌 Conectando ao banco...")
        start_time = time.time()
        
        db_config = load_db_config(setor="cambio", cliente="L2M")
        connection_string = build_connection_string(db_config)
        engine = get_engine(connection_string)
        
        connection_time = (time.time() - start_time) * 1000
        print(f"✅ Conexão estabelecida: {connection_time:.0f}ms")
        
        # Teste de query simples
        print("🔍 Testando query simples...")
        start_time = time.time()
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM boleta LIMIT 1"))
            row = result.fetchone()
            count = row[0] if row else 0
        
        simple_query_time = (time.time() - start_time) * 1000
        print(f"✅ Query simples: {simple_query_time:.0f}ms (count: {count})")
        
        # Teste de query complexa (similar aos KPIs)
        print("🔍 Testando query complexa...")
        start_time = time.time()
        
        complex_query = """
        SELECT 
            SUM(valor_me) as total_volume,
            COUNT(*) as total_operations,
            AVG(valor_me) as avg_volume
        FROM boleta 
        WHERE data_operacao >= CURRENT_DATE - INTERVAL '7 days'
          AND id_cliente = 334
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(complex_query))
            row = result.fetchone()
        
        complex_query_time = (time.time() - start_time) * 1000
        print(f"✅ Query complexa: {complex_query_time:.0f}ms")
        
        if row:
            print(f"   Volume: {row[0]}")
            print(f"   Operações: {row[1]}")
            print(f"   Média: {row[2]}")
        
        return {
            "connection_time_ms": connection_time,
            "simple_query_time_ms": simple_query_time,
            "complex_query_time_ms": complex_query_time
        }
        
    except Exception as e:
        print(f"❌ Erro no teste de banco: {e}")
        return None


def test_cache_performance():
    """Testa a performance do sistema de cache."""
    print("\n💾 TESTANDO PERFORMANCE DO CACHE")
    print("=" * 35)
    
    try:
        from src.caching.unified_cache_system import get_unified_cache
        
        cache = get_unified_cache()
        
        # Teste de escrita
        print("📝 Testando escritas no cache...")
        start_time = time.time()
        
        for i in range(100):
            cache.set("test:performance", {"value": i, "timestamp": time.time()}, 
                     kpi_id=f"test_kpi_{i}", timeframe="week")
        
        write_time = (time.time() - start_time) * 1000
        print(f"✅ 100 escritas: {write_time:.0f}ms ({write_time/100:.1f}ms por operação)")
        
        # Teste de leitura
        print("📖 Testando leituras do cache...")
        start_time = time.time()
        hits = 0
        
        for i in range(100):
            result = cache.get("test:performance", kpi_id=f"test_kpi_{i}", timeframe="week")
            if result:
                hits += 1
        
        read_time = (time.time() - start_time) * 1000
        print(f"✅ 100 leituras: {read_time:.0f}ms ({read_time/100:.1f}ms por operação)")
        print(f"✅ Cache hits: {hits}/100")
        
        # Limpar dados de teste
        cache.clear()
        
        return {
            "write_time_ms": write_time,
            "read_time_ms": read_time,
            "hit_rate": hits / 100
        }
        
    except Exception as e:
        print(f"❌ Erro no teste de cache: {e}")
        return None


def main():
    """Executa todos os testes de debug."""
    print("🐛 DEBUG: IDENTIFICANDO GARGALOS NO CÁLCULO DE KPIS")
    print("=" * 65)
    
    # 1. Testar velocidade do banco
    db_results = test_database_connection_speed()
    
    # 2. Testar performance do cache
    cache_results = test_cache_performance()
    
    # 3. Testar cálculo individual de KPIs
    kpi_results = test_individual_kpi_calculation()
    
    # Análise final
    print(f"\n🎯 ANÁLISE FINAL")
    print("=" * 20)
    
    if db_results:
        if db_results["complex_query_time_ms"] > 5000:
            print("🔴 PROBLEMA: Queries do banco estão muito lentas (>5s)")
        elif db_results["complex_query_time_ms"] > 1000:
            print("🟡 ATENÇÃO: Queries do banco estão lentas (>1s)")
        else:
            print("✅ Banco de dados está respondendo rapidamente")
    
    if cache_results:
        if cache_results["read_time_ms"] > 1000:
            print("🔴 PROBLEMA: Cache está muito lento")
        else:
            print("✅ Cache está funcionando rapidamente")
    
    if kpi_results:
        slow_kpis = [r for r in kpi_results if r.get("time_ms", 0) > 10000]
        if slow_kpis:
            print(f"🔴 PROBLEMA: {len(slow_kpis)} KPIs estão muito lentos (>10s)")
            for kpi in slow_kpis:
                print(f"   - {kpi['kpi_id']}: {kpi['time_ms']:.0f}ms")
        else:
            print("✅ Todos os KPIs estão calculando em tempo aceitável")
    
    print(f"\n💡 RECOMENDAÇÕES:")
    print("1. Se o banco está lento: otimizar queries SQL ou adicionar índices")
    print("2. Se o cache está lento: verificar configuração do Redis")
    print("3. Se KPIs específicos estão lentos: revisar lógica de cálculo")


if __name__ == "__main__":
    main()
