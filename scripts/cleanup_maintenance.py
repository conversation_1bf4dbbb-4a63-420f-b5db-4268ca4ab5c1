#!/usr/bin/env python3
"""
Script de Manutenção e Limpeza Automatizada do DataHero4

Este script realiza limpezas automáticas periódicas para manter o codebase organizado,
removendo arquivos temporários, backups antigos e outros elementos desnecessários.

Uso:
    python scripts/cleanup_maintenance.py [--dry-run] [--verbose]
"""

import os
import sys
import argparse
import shutil
import glob
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Set

class CodebaseCleanup:
    """Classe responsável pela limpeza automatizada do codebase"""
    
    def __init__(self, root_path: str, dry_run: bool = False, verbose: bool = False):
        """
        Inicializa o sistema de limpeza
        
        Args:
            root_path: Caminho raiz do projeto
            dry_run: Se True, apenas simula as operações sem executar
            verbose: Se True, mostra detalhes das operações
        """
        self.root_path = Path(root_path)
        self.dry_run = dry_run
        self.verbose = verbose
        self.removed_files: List[str] = []
        self.removed_dirs: List[str] = []
        
    def log(self, message: str, force: bool = False) -> None:
        """Log condicionado ao modo verbose"""
        if self.verbose or force:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def remove_old_backups(self, days_old: int = 30) -> None:
        """
        Remove arquivos de backup mais antigos que X dias
        
        Args:
            days_old: Número de dias para considerar um backup antigo
        """
        self.log(f"🗑️  Removendo backups com mais de {days_old} dias...")
        
        cutoff_date = datetime.now() - timedelta(days=days_old)
        patterns = [
            "**/*.backup",
            "**/backup_*.sql", 
            "**/backup_*.json",
            "**/*_backup_*",
            "**/backups/**/*"
        ]
        
        for pattern in patterns:
            for file_path in self.root_path.glob(pattern):
                if file_path.is_file():
                    # Verifica idade do arquivo
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        self._remove_file(file_path)
    
    def remove_temp_files(self) -> None:
        """Remove arquivos temporários de desenvolvimento"""
        self.log("🧹 Removendo arquivos temporários...")
        
        temp_patterns = [
            "**/temp/**/*",
            "**/*.tmp",
            "**/*.temp",
            "**/profiling_results_*.json",
            "**/cache_test_results_*.json",
            "**/test_response.json",
            "**/response.json",
            "**/*_temp_*",
            "**/*.swp",
            "**/*.swo"
        ]
        
        for pattern in temp_patterns:
            for file_path in self.root_path.glob(pattern):
                if file_path.is_file():
                    self._remove_file(file_path)
    
    def remove_empty_dirs(self) -> None:
        """Remove diretórios vazios"""
        self.log("📁 Removendo diretórios vazios...")
        
        for root, dirs, files in os.walk(self.root_path, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                try:
                    if not any(dir_path.iterdir()):  # Diretório vazio
                        # Pula diretórios importantes que podem estar vazios temporariamente
                        if dir_name not in ['.git', 'node_modules', 'venv', '.venv']:
                            self._remove_dir(dir_path)
                except OSError:
                    continue
    
    def clean_old_logs(self, days_old: int = 7) -> None:
        """
        Remove logs antigos
        
        Args:
            days_old: Número de dias para manter os logs
        """
        self.log(f"📋 Removendo logs com mais de {days_old} dias...")
        
        cutoff_date = datetime.now() - timedelta(days=days_old)
        log_patterns = [
            "**/logs/*.log.*",
            "**/*.log.[0-9]*",
            "**/datahero_chat.log.*"
        ]
        
        for pattern in log_patterns:
            for file_path in self.root_path.glob(pattern):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        self._remove_file(file_path)
    
    def organize_documentation(self) -> None:
        """Organiza documentação dispersa"""
        self.log("📚 Organizando documentação...")
        
        # Move arquivos de planejamento soltos do root
        planning_files = [
            "*-plan.md",
            "*-implementation-plan.md", 
            "*-strategy.md"
        ]
        
        docs_planning = self.root_path / "docs" / "planning"
        docs_planning.mkdir(exist_ok=True)
        
        for pattern in planning_files:
            for file_path in self.root_path.glob(pattern):
                if file_path.parent == self.root_path:  # Apenas do root
                    target = docs_planning / file_path.name
                    self._move_file(file_path, target)
    
    def _remove_file(self, file_path: Path) -> None:
        """Remove um arquivo com logging"""
        try:
            if not self.dry_run:
                file_path.unlink()
            self.removed_files.append(str(file_path.relative_to(self.root_path)))
            self.log(f"  ✅ Removido: {file_path.relative_to(self.root_path)}")
        except Exception as e:
            self.log(f"  ❌ Erro ao remover {file_path}: {e}")
    
    def _remove_dir(self, dir_path: Path) -> None:
        """Remove um diretório com logging"""
        try:
            if not self.dry_run:
                dir_path.rmdir()
            self.removed_dirs.append(str(dir_path.relative_to(self.root_path)))
            self.log(f"  ✅ Removido diretório: {dir_path.relative_to(self.root_path)}")
        except Exception as e:
            self.log(f"  ❌ Erro ao remover diretório {dir_path}: {e}")
    
    def _move_file(self, source: Path, target: Path) -> None:
        """Move um arquivo com logging"""
        try:
            if not self.dry_run:
                shutil.move(str(source), str(target))
            self.log(f"  ✅ Movido: {source.relative_to(self.root_path)} → {target.relative_to(self.root_path)}")
        except Exception as e:
            self.log(f"  ❌ Erro ao mover {source}: {e}")
    
    def run_full_cleanup(self) -> None:
        """Executa limpeza completa"""
        self.log("🚀 Iniciando limpeza automatizada do DataHero4", force=True)
        
        if self.dry_run:
            self.log("🔍 MODO DRY-RUN: Nenhum arquivo será realmente removido", force=True)
        
        # Executa todas as limpezas
        self.remove_old_backups()
        self.remove_temp_files()
        self.clean_old_logs()
        self.organize_documentation()
        self.remove_empty_dirs()
        
        # Relatório final
        self._generate_report()
    
    def _generate_report(self) -> None:
        """Gera relatório da limpeza"""
        self.log("", force=True)
        self.log("📊 RELATÓRIO DE LIMPEZA", force=True)
        self.log("=" * 50, force=True)
        self.log(f"Arquivos removidos: {len(self.removed_files)}", force=True)
        self.log(f"Diretórios removidos: {len(self.removed_dirs)}", force=True)
        
        if self.verbose and self.removed_files:
            self.log("\nArquivos removidos:", force=True)
            for file in self.removed_files[:10]:  # Mostra apenas os primeiros 10
                self.log(f"  - {file}", force=True)
            if len(self.removed_files) > 10:
                self.log(f"  ... e mais {len(self.removed_files) - 10} arquivos", force=True)
        
        if not self.dry_run:
            self.log("✅ Limpeza concluída com sucesso!", force=True)
        else:
            self.log("🔍 Simulação concluída. Use sem --dry-run para executar.", force=True)


def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description="Script de manutenção e limpeza automatizada do DataHero4"
    )
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Simula as operações sem executar (modo de teste)"
    )
    parser.add_argument(
        "--verbose", "-v", 
        action="store_true", 
        help="Mostra detalhes das operações"
    )
    parser.add_argument(
        "--backup-days", 
        type=int, 
        default=30, 
        help="Dias para manter backups (padrão: 30)"
    )
    parser.add_argument(
        "--log-days", 
        type=int, 
        default=7, 
        help="Dias para manter logs (padrão: 7)"
    )
    
    args = parser.parse_args()
    
    # Determina o caminho raiz do projeto
    script_dir = Path(__file__).parent
    root_path = script_dir.parent
    
    # Verifica se estamos no projeto correto
    if not (root_path / "apps" / "backend").exists():
        print("❌ Erro: Este script deve ser executado do diretório do projeto DataHero4")
        sys.exit(1)
    
    # Executa a limpeza
    cleanup = CodebaseCleanup(
        root_path=str(root_path),
        dry_run=args.dry_run,
        verbose=args.verbose
    )
    
    cleanup.run_full_cleanup()


if __name__ == "__main__":
    main() 