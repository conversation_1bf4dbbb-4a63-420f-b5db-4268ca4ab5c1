-- Fix conversation tables by adding missing columns
-- to match Python model expectations

\echo '=== ADDING MISSING COLUMNS TO conversation_threads ==='

-- Add missing columns to conversation_threads
ALTER TABLE conversation_threads ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT false;
ALTER TABLE conversation_threads ADD COLUMN IF NOT EXISTS context_optimization_enabled BOOLEAN DEFAULT true;  
ALTER TABLE conversation_threads ADD COLUMN IF NOT EXISTS streaming_enabled BOOLEAN DEFAULT true;

-- Add comments for new columns
COMMENT ON COLUMN conversation_threads.is_archived IS 'Whether the thread is archived';
COMMENT ON COLUMN conversation_threads.context_optimization_enabled IS 'Enable context optimization';
COMMENT ON COLUMN conversation_threads.streaming_enabled IS 'Enable streaming responses';

\echo '=== ADDING MISSING COLUMNS TO conversation_messages ==='

-- Add missing columns to conversation_messages  
ALTER TABLE conversation_messages ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE conversation_messages ADD COLUMN IF NOT EXISTS query_id VARCHAR(100);
ALTER TABLE conversation_messages ADD COLUMN IF NOT EXISTS reply_to_message_id UUID;
ALTER TABLE conversation_messages ADD COLUMN IF NOT EXISTS is_edited BOOLEAN DEFAULT false;
ALTER TABLE conversation_messages ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT false;

-- Add foreign key constraint for reply_to_message_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_conversation_messages_reply_to'
        AND table_name = 'conversation_messages'
    ) THEN
        ALTER TABLE conversation_messages 
        ADD CONSTRAINT fk_conversation_messages_reply_to 
        FOREIGN KEY (reply_to_message_id) REFERENCES conversation_messages(id);
    END IF;
END $$;

-- Add comments for new columns
COMMENT ON COLUMN conversation_messages.updated_at IS 'Last update timestamp';
COMMENT ON COLUMN conversation_messages.query_id IS 'Associated query ID from ask API';
COMMENT ON COLUMN conversation_messages.reply_to_message_id IS 'ID of message being replied to';
COMMENT ON COLUMN conversation_messages.is_edited IS 'Whether message was edited';
COMMENT ON COLUMN conversation_messages.is_deleted IS 'Whether message was soft deleted';

\echo '=== ADDING INDEXES FOR NEW COLUMNS ==='

-- Add indexes for new columns
CREATE INDEX IF NOT EXISTS idx_conversation_threads_is_archived ON conversation_threads(is_archived);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_updated_at ON conversation_messages(updated_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_query_id ON conversation_messages(query_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_reply_to ON conversation_messages(reply_to_message_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_is_edited ON conversation_messages(is_edited);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_is_deleted ON conversation_messages(is_deleted);

\echo '=== UPDATING TRIGGERS ==='

-- Update trigger for conversation_messages updated_at
CREATE OR REPLACE FUNCTION update_message_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_message_updated_at
    BEFORE UPDATE ON conversation_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_message_updated_at();

\echo '=== VERIFICATION ==='

-- Verify all columns exist
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('conversation_threads', 'conversation_messages')
AND table_schema = 'public'
ORDER BY table_name, ordinal_position;

\echo '=== SCHEMA FIX COMPLETED ==='