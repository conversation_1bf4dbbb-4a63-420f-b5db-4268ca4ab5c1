#!/usr/bin/env python3
"""
Implementação de melhorias imediatas para aumentar a taxa de satisfação.
Foca nos problemas mais críticos identificados na análise.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'apps', 'backend'))

import requests
import json
from datetime import datetime
from typing import Dict, List, Any

def test_problematic_questions():
    """
    Testa as perguntas que geraram feedbacks negativos para entender o gap
    entre funcionamento técnico e satisfação do usuário.
    """
    
    print("🔍 TESTE DAS PERGUNTAS PROBLEMÁTICAS")
    print("=" * 50)
    print("Objetivo: Entender por que testes passam mas usuários estão insatisfeitos")
    print()
    
    # Perguntas exatas dos feedbacks negativos mais críticos
    problematic_questions = [
        {
            "id": "prob_001",
            "question": "mostre boletas de janeiro 2025",
            "category": "sql_incorreto",
            "user_complaint": "SQL não está correto",
            "expected_issue": "SQL pode estar tecnicamente válido mas logicamente incorreto"
        },
        {
            "id": "prob_002", 
            "question": "informe todas as boletas de janeiro 2025 com nome do cliente",
            "category": "other",
            "user_complaint": "informei o ano e também não trouxe dados",
            "expected_issue": "Pode não ter dados para janeiro 2025 mas resposta não explica isso"
        },
        {
            "id": "prob_003",
            "question": "clientes com operações em janeiro",
            "category": "join_ausente",
            "user_complaint": "Precisa fazer JOIN com tabela pessoa",
            "expected_issue": "SQL pode estar sem JOIN necessário"
        },
        {
            "id": "prob_004",
            "question": "boletas de dezembro com valor alto",
            "category": "filtro_ausente", 
            "user_complaint": "Faltou filtro de valor e coluna de data errada",
            "expected_issue": "Filtros ausentes ou colunas incorretas"
        },
        {
            "id": "prob_005",
            "question": "Qual foi o volume total de transações em USD hoje?",
            "category": "wrong_data",
            "user_complaint": "A query está retornando null quando deveria mostrar valores reais",
            "expected_issue": "Dados existem mas query não os encontra"
        }
    ]
    
    base_url = "http://localhost:8000"
    results = []
    
    for i, case in enumerate(problematic_questions, 1):
        print(f"🧪 TESTE {i}: {case['id']}")
        print(f"❓ Pergunta: {case['question']}")
        print(f"📂 Categoria: {case['category']}")
        print(f"💬 Reclamação: {case['user_complaint']}")
        print(f"🔍 Suspeita: {case['expected_issue']}")
        print()
        
        # Executar pergunta
        try:
            response = requests.post(
                f"{base_url}/ask",
                json={
                    "question": case["question"],
                    "client_id": "L2M", 
                    "sector": "cambio"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Analisar resposta detalhadamente
                analysis = analyze_response_quality(case, data)
                results.append(analysis)
                
                print(f"✅ Resposta recebida")
                print(f"📊 SQL: {data.get('sql_query', 'N/A')[:100]}...")
                print(f"📈 Resultados: {len(data.get('results', []))} registros")
                print(f"💭 Resposta: {data.get('formatted_response', 'N/A')[:100]}...")
                print()
                
                # Mostrar análise de qualidade
                print(f"🔍 ANÁLISE DE QUALIDADE:")
                for issue in analysis['quality_issues']:
                    print(f"   ❌ {issue}")
                
                for strength in analysis['strengths']:
                    print(f"   ✅ {strength}")
                
                print(f"📊 Score de qualidade: {analysis['quality_score']:.1f}/10")
                print()
                
            else:
                print(f"❌ Erro HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Erro: {e}")
        
        print("-" * 50)
    
    # Análise consolidada
    if results:
        print(f"\n📊 ANÁLISE CONSOLIDADA")
        print("=" * 50)
        
        avg_quality = sum(r['quality_score'] for r in results) / len(results)
        print(f"📈 Score médio de qualidade: {avg_quality:.1f}/10")
        
        # Problemas mais comuns
        all_issues = []
        for r in results:
            all_issues.extend(r['quality_issues'])
        
        from collections import Counter
        issue_freq = Counter(all_issues)
        
        print(f"\n🚨 Problemas mais frequentes:")
        for issue, freq in issue_freq.most_common(5):
            print(f"   {issue}: {freq} casos")
        
        # Recomendações específicas
        print(f"\n💡 RECOMENDAÇÕES ESPECÍFICAS:")
        
        if avg_quality < 5:
            print("   🚨 CRÍTICO: Qualidade muito baixa - ação imediata necessária")
        elif avg_quality < 7:
            print("   ⚠️ MODERADO: Melhorias significativas necessárias")
        else:
            print("   ✅ BOM: Pequenos ajustes necessários")
        
        # Ações prioritárias baseadas nos problemas
        if "SQL sem JOIN necessário" in [issue for issue, _ in issue_freq.most_common(3)]:
            print("   1. PRIORIDADE ALTA: Melhorar detecção de necessidade de JOINs")
        
        if "Filtros ausentes" in [issue for issue, _ in issue_freq.most_common(3)]:
            print("   2. PRIORIDADE ALTA: Implementar validação de filtros obrigatórios")
        
        if "Resposta não explica ausência de dados" in [issue for issue, _ in issue_freq.most_common(3)]:
            print("   3. PRIORIDADE MÉDIA: Melhorar mensagens explicativas")
    
    return results

def analyze_response_quality(test_case: Dict[str, Any], response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analisa a qualidade da resposta do ponto de vista do usuário.
    """
    
    quality_issues = []
    strengths = []
    quality_score = 10.0  # Começa com nota máxima e deduz pontos
    
    sql_query = response_data.get('sql_query', '')
    results = response_data.get('results', [])
    formatted_response = response_data.get('formatted_response', '')
    
    # 1. Análise do SQL baseada na categoria do problema
    if test_case['category'] == 'join_ausente':
        if 'JOIN' not in sql_query.upper():
            quality_issues.append("SQL sem JOIN necessário")
            quality_score -= 3.0
        else:
            strengths.append("JOIN presente no SQL")
    
    if test_case['category'] == 'filtro_ausente':
        if 'WHERE' not in sql_query.upper():
            quality_issues.append("Filtros ausentes")
            quality_score -= 2.5
        elif 'valor' in test_case['question'].lower() and 'valor' not in sql_query.lower():
            quality_issues.append("Filtro de valor ausente")
            quality_score -= 2.0
        else:
            strengths.append("Filtros presentes")
    
    if test_case['category'] == 'sql_incorreto':
        # Verificar se SQL tem problemas óbvios
        if 'data_criacao' in sql_query and 'janeiro' in test_case['question']:
            quality_issues.append("Usando coluna data_criacao em vez de data_operacao")
            quality_score -= 2.5
        
        if '2016' in sql_query or '2020' in sql_query:
            quality_issues.append("Ano incorreto no SQL")
            quality_score -= 3.0
    
    # 2. Análise dos resultados
    if len(results) == 0:
        if 'janeiro 2025' in test_case['question']:
            quality_issues.append("Sem dados para janeiro 2025 - pode ser esperado")
            quality_score -= 1.0
        else:
            quality_issues.append("Nenhum resultado retornado")
            quality_score -= 2.0
    else:
        strengths.append(f"{len(results)} resultados encontrados")
    
    # 3. Análise da resposta formatada
    if not formatted_response:
        quality_issues.append("Resposta formatada ausente")
        quality_score -= 1.5
    elif len(formatted_response) < 50:
        quality_issues.append("Resposta muito curta")
        quality_score -= 1.0
    else:
        strengths.append("Resposta bem formatada")
    
    # 4. Análise de explicação para casos sem dados
    if len(results) == 0 and 'não' not in formatted_response.lower():
        quality_issues.append("Resposta não explica ausência de dados")
        quality_score -= 1.5
    
    # 5. Verificação de campos de validação
    sql_valid = response_data.get('sql_valid', False)
    if not sql_valid:
        quality_issues.append("SQL marcado como inválido")
        quality_score -= 1.0
    else:
        strengths.append("SQL validado como correto")
    
    # 6. Análise específica por pergunta
    if 'USD' in test_case['question'] and len(results) == 0:
        quality_issues.append("Pergunta sobre USD sem resultados - verificar se dados existem")
        quality_score -= 1.5
    
    if 'hoje' in test_case['question'] and 'ontem' in sql_query:
        quality_issues.append("Confusão temporal: pergunta sobre hoje mas SQL busca ontem")
        quality_score -= 3.0
    
    # Garantir que score não seja negativo
    quality_score = max(0.0, quality_score)
    
    return {
        'test_id': test_case['id'],
        'quality_score': quality_score,
        'quality_issues': quality_issues,
        'strengths': strengths,
        'sql_query': sql_query,
        'result_count': len(results),
        'has_formatted_response': bool(formatted_response)
    }

def implement_immediate_improvements():
    """
    Implementa melhorias imediatas baseadas na análise.
    """
    
    print(f"\n🔧 IMPLEMENTANDO MELHORIAS IMEDIATAS")
    print("=" * 50)
    
    improvements = [
        {
            "area": "Validação de SQL",
            "action": "Adicionar verificação de JOINs obrigatórios",
            "impact": "Alto",
            "effort": "Médio"
        },
        {
            "area": "Geração de SQL", 
            "action": "Melhorar prompts para incluir filtros necessários",
            "impact": "Alto",
            "effort": "Baixo"
        },
        {
            "area": "Resposta ao usuário",
            "action": "Melhorar mensagens quando não há dados",
            "impact": "Médio",
            "effort": "Baixo"
        },
        {
            "area": "Validação temporal",
            "action": "Verificar consistência entre pergunta e SQL para datas",
            "impact": "Alto",
            "effort": "Médio"
        }
    ]
    
    print(f"📋 Melhorias prioritárias identificadas:")
    for i, improvement in enumerate(improvements, 1):
        print(f"   {i}. {improvement['area']}: {improvement['action']}")
        print(f"      Impacto: {improvement['impact']}, Esforço: {improvement['effort']}")
    
    print(f"\n⏱️ Cronograma de implementação:")
    print(f"   Imediato (hoje): Melhorar mensagens de resposta")
    print(f"   Esta semana: Implementar validações de JOIN e filtros")
    print(f"   Próxima semana: Melhorar prompts de geração de SQL")
    print(f"   Em 2 semanas: Validação temporal completa")
    
    return improvements

if __name__ == "__main__":
    print("🚀 Iniciando análise de qualidade e melhorias imediatas...")
    
    # Testar perguntas problemáticas
    results = test_problematic_questions()
    
    # Implementar melhorias
    improvements = implement_immediate_improvements()
    
    print(f"\n✅ Análise concluída!")
    print(f"📊 {len(results)} perguntas analisadas")
    print(f"🔧 {len(improvements)} melhorias identificadas")
    print(f"\n🎯 Próximo passo: Implementar melhorias para aumentar satisfação de 10% para 80%")
