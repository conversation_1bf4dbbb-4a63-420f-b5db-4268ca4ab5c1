-- Check all existing tables in Railway PostgreSQL
\echo '=== ALL TABLES IN DATABASE ==='
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

\echo '=== MISSING CONVERSATION TABLES CHECK ==='
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'conversation_threads') 
    THEN '✅ conversation_threads EXISTS'
    ELSE '❌ conversation_threads MISSING'
  END as threads_status,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'conversation_messages') 
    THEN '✅ conversation_messages EXISTS'
    ELSE '❌ conversation_messages MISSING'
  END as messages_status;

\echo '=== LEARNING TABLES CHECK ==='
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feedback_corrections') 
    THEN '✅ feedback_corrections EXISTS'
    ELSE '❌ feedback_corrections MISSING'
  END as feedback_status,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'query_cache') 
    THEN '✅ query_cache EXISTS'
    ELSE '❌ query_cache MISSING'
  END as cache_status,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'query_history') 
    THEN '✅ query_history EXISTS'
    ELSE '❌ query_history MISSING'
  END as history_status;