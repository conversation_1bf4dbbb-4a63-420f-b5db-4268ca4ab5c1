-- Script para Corrigir Definições dos KPIs Críticos
-- DataHero4 - Dashboard Frontend
-- Data: 2025-07-06

-- 1. Corrigir prioridade do KPI operations_per_analyst
-- (Marcar como prioritário conforme definido no critical_kpis.py)
UPDATE kpi_definitions 
SET is_priority = true 
WHERE id = 'operations_per_analyst';

-- 2. Corrigir fórmula do spread para percentual
-- (Atualizar fórmula para retornar percentual ao invés de valor absoluto)
UPDATE kpi_definitions 
SET formula = 'Para compra: ((Taxa de mercado - Taxa aplicada ao cliente) / Taxa de mercado) × 100
Para venda: ((Taxa aplicada ao cliente - Taxa de mercado) / Taxa de mercado) × 100
Implementação SQL: AVG(((taxa_cambio_venda - taxa_cambio_compra) / taxa_cambio_compra) * 100)'
WHERE id = 'average_spread';

-- 3. Atualizar display_order para garantir ordem correta dos KPIs críticos
UPDATE kpi_definitions 
SET display_order = 1 
WHERE id = 'total_volume';

UPDATE kpi_definitions 
SET display_order = 2 
WHERE id = 'average_spread';

UPDATE kpi_definitions 
SET display_order = 3 
WHERE id = 'conversion_rate';

UPDATE kpi_definitions 
SET display_order = 4 
WHERE id = 'average_ticket';

UPDATE kpi_definitions 
SET display_order = 5 
WHERE id = 'retention_rate';

UPDATE kpi_definitions 
SET display_order = 6 
WHERE id = 'operations_per_analyst';

-- 4. Verificar se todas as correções foram aplicadas
SELECT 
    id,
    name,
    is_priority,
    display_order,
    format_type,
    category,
    LEFT(formula, 100) as formula_preview
FROM kpi_definitions 
WHERE id IN (
    'total_volume',
    'average_spread', 
    'conversion_rate',
    'average_ticket',
    'retention_rate',
    'operations_per_analyst'
)
ORDER BY display_order;

-- 5. Verificar total de KPIs prioritários (deve ser 6)
SELECT 
    COUNT(*) as total_kpis_prioritarios,
    'Deve ser 6 KPIs críticos' as observacao
FROM kpi_definitions 
WHERE is_priority = true 
    AND sector = 'cambio' 
    AND is_active = true;

-- 6. Listar todos os KPIs críticos para validação
SELECT 
    'KPIs Críticos Configurados:' as titulo,
    STRING_AGG(id, ', ' ORDER BY display_order) as kpis_criticos
FROM kpi_definitions 
WHERE is_priority = true 
    AND sector = 'cambio' 
    AND is_active = true; 