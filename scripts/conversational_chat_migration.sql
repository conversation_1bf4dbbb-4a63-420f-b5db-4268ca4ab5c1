-- DataHero4 Conversational Chat Database Migration
-- ================================================
-- 
-- This migration adds support for conversational chat functionality
-- while preserving all existing functionality and data.
--
-- Tables Created:
-- 1. conversation_threads - Thread metadata and lifecycle
-- 2. conversation_messages - Individual messages in conversations  
-- 3. thread_context_windows - Context optimization data
-- 4. conversation_summaries - Conversation summaries for memory
-- 5. conversation_feedback - Feedback specific to conversation messages
--
-- Extensions:
-- - query_history table extended with thread_id support
-- - Indexes for performance optimization
-- - Foreign key relationships for data integrity

-- ============================================================================
-- 1. CONVERSATION THREADS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS conversation_threads (
    -- Primary identification
    id VARCHAR(100) PRIMARY KEY,
    
    -- User and client association
    user_id VARCHAR(50) NOT NULL,
    client_id VARCHAR(50) DEFAULT 'L2M',
    sector VARCHAR(50) DEFAULT 'cambio',
    
    -- Thread metadata
    title VARCHAR(200),
    description TEXT,
    
    -- Lifecycle tracking
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE,
    
    -- Statistics
    message_count INTEGER DEFAULT 0,
    total_tokens_used INTEGER DEFAULT 0,
    total_processing_time REAL DEFAULT 0.0,
    
    -- Status and flags
    is_active BOOLEAN DEFAULT TRUE,
    is_archived BOOLEAN DEFAULT FALSE,
    
    -- Performance and optimization
    context_optimization_enabled BOOLEAN DEFAULT TRUE,
    streaming_enabled BOOLEAN DEFAULT TRUE,
    
    -- Flexible metadata storage
    metadata JSONB DEFAULT '{}',
    
    -- Performance tracking
    performance_metrics JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT valid_message_count CHECK (message_count >= 0),
    CONSTRAINT valid_tokens CHECK (total_tokens_used >= 0),
    CONSTRAINT valid_processing_time CHECK (total_processing_time >= 0)
);

-- Indexes for conversation_threads
CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_id ON conversation_threads(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_client_sector ON conversation_threads(client_id, sector);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_created_at ON conversation_threads(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_last_message ON conversation_threads(last_message_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_active ON conversation_threads(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_active ON conversation_threads(user_id, is_active) WHERE is_active = TRUE;

-- ============================================================================
-- 2. CONVERSATION MESSAGES TABLE  
-- ============================================================================

CREATE TABLE IF NOT EXISTS conversation_messages (
    -- Primary identification
    id VARCHAR(100) PRIMARY KEY,
    
    -- Thread association
    thread_id VARCHAR(100) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    
    -- Message content and metadata
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    
    -- Processing information
    query_id VARCHAR(100), -- Link to query_history for assistant messages
    tokens_used INTEGER DEFAULT 0,
    processing_time REAL DEFAULT 0.0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Message relationships
    reply_to_message_id VARCHAR(100), -- For threaded conversations
    
    -- Status and flags
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    
    -- Feedback integration
    has_feedback BOOLEAN DEFAULT FALSE,
    feedback_score REAL, -- Average feedback score
    feedback_count INTEGER DEFAULT 0,
    
    -- Performance and analysis data
    analysis_data JSONB DEFAULT '{}', -- SQL queries, visualizations, etc.
    
    -- Flexible metadata storage
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT valid_tokens_used CHECK (tokens_used >= 0),
    CONSTRAINT valid_processing_time CHECK (processing_time >= 0.0),
    CONSTRAINT valid_feedback_score CHECK (feedback_score IS NULL OR (feedback_score >= 0 AND feedback_score <= 5)),
    CONSTRAINT valid_feedback_count CHECK (feedback_count >= 0)
);

-- Indexes for conversation_messages
CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_id ON conversation_messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_role ON conversation_messages(role);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_query_id ON conversation_messages(query_id) WHERE query_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_created ON conversation_messages(thread_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_feedback ON conversation_messages(has_feedback) WHERE has_feedback = TRUE;

-- ============================================================================
-- 3. THREAD CONTEXT WINDOWS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS thread_context_windows (
    -- Primary identification
    id VARCHAR(100) PRIMARY KEY,
    
    -- Thread association
    thread_id VARCHAR(100) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    
    -- Context window data
    message_ids TEXT[] NOT NULL, -- Array of message IDs included in context
    total_tokens INTEGER NOT NULL,
    optimization_strategy VARCHAR(50) NOT NULL,
    
    -- Relevance scoring
    relevance_scores JSONB NOT NULL DEFAULT '{}', -- message_id -> relevance_score mapping
    relevance_threshold REAL DEFAULT 0.3,
    
    -- Performance metrics
    compression_ratio REAL, -- Original tokens / optimized tokens
    optimization_time REAL DEFAULT 0.0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Context metadata
    context_summary TEXT,
    key_topics TEXT[],
    
    -- Flexible metadata
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT valid_total_tokens CHECK (total_tokens > 0),
    CONSTRAINT valid_compression_ratio CHECK (compression_ratio IS NULL OR compression_ratio > 0),
    CONSTRAINT valid_optimization_time CHECK (optimization_time >= 0),
    CONSTRAINT valid_relevance_threshold CHECK (relevance_threshold >= 0 AND relevance_threshold <= 1)
);

-- Indexes for thread_context_windows
CREATE INDEX IF NOT EXISTS idx_thread_context_windows_thread_id ON thread_context_windows(thread_id);
CREATE INDEX IF NOT EXISTS idx_thread_context_windows_created_at ON thread_context_windows(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_thread_context_windows_strategy ON thread_context_windows(optimization_strategy);

-- ============================================================================
-- 4. CONVERSATION SUMMARIES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS conversation_summaries (
    -- Primary identification
    id VARCHAR(100) PRIMARY KEY,
    
    -- Thread association
    thread_id VARCHAR(100) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    
    -- Summary content
    summary_text TEXT NOT NULL,
    key_topics TEXT[] DEFAULT '{}',
    important_queries TEXT[] DEFAULT '{}',
    
    -- User preferences extracted from conversation
    user_preferences JSONB DEFAULT '{}',
    
    -- Coverage information
    covers_messages_from TIMESTAMP WITH TIME ZONE NOT NULL,
    covers_messages_until TIMESTAMP WITH TIME ZONE NOT NULL,
    message_count_covered INTEGER NOT NULL,
    
    -- Summary metadata
    token_count INTEGER DEFAULT 0,
    compression_ratio REAL, -- Original tokens / summary tokens
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Flexible metadata
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT valid_message_count_covered CHECK (message_count_covered > 0),
    CONSTRAINT valid_token_count CHECK (token_count >= 0),
    CONSTRAINT valid_summary_compression CHECK (compression_ratio IS NULL OR compression_ratio > 0),
    CONSTRAINT valid_coverage_period CHECK (covers_messages_until >= covers_messages_from)
);

-- Indexes for conversation_summaries
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_thread_id ON conversation_summaries(thread_id);
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_created_at ON conversation_summaries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_active ON conversation_summaries(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_conversation_summaries_coverage ON conversation_summaries(covers_messages_until DESC);

-- ============================================================================
-- 5. CONVERSATION FEEDBACK TABLE (Extension of existing feedback system)
-- ============================================================================

CREATE TABLE IF NOT EXISTS conversation_feedback (
    -- Primary identification
    id VARCHAR(100) PRIMARY KEY,
    
    -- Associations
    thread_id VARCHAR(100) NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    message_id VARCHAR(100) NOT NULL REFERENCES conversation_messages(id) ON DELETE CASCADE,
    query_id VARCHAR(100), -- Link to query_history if applicable
    
    -- User information
    user_id VARCHAR(50) NOT NULL,
    
    -- Feedback content
    feedback_type VARCHAR(20) NOT NULL CHECK (feedback_type IN ('positive', 'negative', 'neutral')),
    feedback_score INTEGER CHECK (feedback_score >= 1 AND feedback_score <= 5),
    feedback_text TEXT,
    
    -- Feedback categories
    feedback_categories TEXT[] DEFAULT '{}', -- e.g., ['accuracy', 'helpfulness', 'clarity']
    
    -- Context information
    conversation_context JSONB DEFAULT '{}',
    
    -- Processing status
    is_processed BOOLEAN DEFAULT FALSE,
    processing_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- Flexible metadata
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT unique_user_message_feedback UNIQUE (user_id, message_id, feedback_type)
);

-- Indexes for conversation_feedback
CREATE INDEX IF NOT EXISTS idx_conversation_feedback_thread_id ON conversation_feedback(thread_id);
CREATE INDEX IF NOT EXISTS idx_conversation_feedback_message_id ON conversation_feedback(message_id);
CREATE INDEX IF NOT EXISTS idx_conversation_feedback_user_id ON conversation_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_feedback_type ON conversation_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_conversation_feedback_created_at ON conversation_feedback(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_feedback_unprocessed ON conversation_feedback(is_processed) WHERE is_processed = FALSE;

-- ============================================================================
-- 6. EXTEND EXISTING QUERY_HISTORY TABLE
-- ============================================================================

-- Add thread_id column to existing query_history table for conversation support
DO $$ 
BEGIN
    -- Check if thread_id column already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'query_history' 
        AND column_name = 'thread_id'
    ) THEN
        ALTER TABLE query_history ADD COLUMN thread_id VARCHAR(100);
        
        -- Add index for thread_id
        CREATE INDEX IF NOT EXISTS idx_query_history_thread_id ON query_history(thread_id);
        
        -- Add index for session_id + thread_id combination
        CREATE INDEX IF NOT EXISTS idx_query_history_session_thread ON query_history(session_id, thread_id);
    END IF;
END $$;

-- Add conversation context column to query_history
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'query_history' 
        AND column_name = 'conversation_context'
    ) THEN
        ALTER TABLE query_history ADD COLUMN conversation_context JSONB DEFAULT '{}';
    END IF;
END $$;

-- ============================================================================
-- 7. PERFORMANCE OPTIMIZATION VIEWS
-- ============================================================================

-- View for active conversations with latest message
CREATE OR REPLACE VIEW active_conversations AS
SELECT 
    ct.id,
    ct.user_id,
    ct.client_id,
    ct.sector,
    ct.title,
    ct.created_at,
    ct.last_message_at,
    ct.message_count,
    ct.total_tokens_used,
    -- Latest message preview
    cm.content as latest_message,
    cm.role as latest_message_role,
    cm.created_at as latest_message_time
FROM conversation_threads ct
LEFT JOIN conversation_messages cm ON cm.thread_id = ct.id 
    AND cm.created_at = ct.last_message_at
WHERE ct.is_active = TRUE
ORDER BY ct.last_message_at DESC NULLS LAST;

-- View for conversation statistics
CREATE OR REPLACE VIEW conversation_stats AS
SELECT 
    ct.user_id,
    ct.client_id,
    ct.sector,
    COUNT(*) as total_conversations,
    COUNT(*) FILTER (WHERE ct.is_active) as active_conversations,
    SUM(ct.message_count) as total_messages,
    SUM(ct.total_tokens_used) as total_tokens,
    AVG(ct.message_count) as avg_messages_per_conversation,
    MAX(ct.last_message_at) as last_activity
FROM conversation_threads ct
GROUP BY ct.user_id, ct.client_id, ct.sector;

-- ============================================================================
-- 8. TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Function to update conversation thread statistics
CREATE OR REPLACE FUNCTION update_conversation_thread_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update message count and last message time
    UPDATE conversation_threads 
    SET 
        message_count = (
            SELECT COUNT(*) 
            FROM conversation_messages 
            WHERE thread_id = NEW.thread_id 
            AND is_deleted = FALSE
        ),
        last_message_at = NEW.created_at,
        updated_at = NOW(),
        total_tokens_used = (
            SELECT COALESCE(SUM(tokens_used), 0)
            FROM conversation_messages 
            WHERE thread_id = NEW.thread_id 
            AND is_deleted = FALSE
        )
    WHERE id = NEW.thread_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update thread stats when messages are added
DROP TRIGGER IF EXISTS trigger_update_thread_stats ON conversation_messages;
CREATE TRIGGER trigger_update_thread_stats
    AFTER INSERT OR UPDATE ON conversation_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_thread_stats();

-- Function to update message feedback statistics
CREATE OR REPLACE FUNCTION update_message_feedback_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update message feedback statistics
    UPDATE conversation_messages 
    SET 
        has_feedback = TRUE,
        feedback_count = (
            SELECT COUNT(*) 
            FROM conversation_feedback 
            WHERE message_id = NEW.message_id
        ),
        feedback_score = (
            SELECT AVG(feedback_score) 
            FROM conversation_feedback 
            WHERE message_id = NEW.message_id 
            AND feedback_score IS NOT NULL
        )
    WHERE id = NEW.message_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update message feedback stats
DROP TRIGGER IF EXISTS trigger_update_message_feedback_stats ON conversation_feedback;
CREATE TRIGGER trigger_update_message_feedback_stats
    AFTER INSERT OR UPDATE ON conversation_feedback
    FOR EACH ROW
    EXECUTE FUNCTION update_message_feedback_stats();

-- ============================================================================
-- 9. SAMPLE DATA AND VALIDATION
-- ============================================================================

-- Insert sample conversation thread for testing
INSERT INTO conversation_threads (
    id, user_id, client_id, sector, title, created_at
) VALUES (
    'sample-thread-001', 
    'test-user', 
    'L2M', 
    'cambio', 
    'Conversa de Teste - Análise de Vendas',
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Validation queries
DO $$
BEGIN
    -- Verify tables were created
    ASSERT (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'conversation_threads') = 1,
           'conversation_threads table not created';
    
    ASSERT (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'conversation_messages') = 1,
           'conversation_messages table not created';
           
    ASSERT (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'thread_context_windows') = 1,
           'thread_context_windows table not created';
           
    ASSERT (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'conversation_summaries') = 1,
           'conversation_summaries table not created';
           
    ASSERT (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'conversation_feedback') = 1,
           'conversation_feedback table not created';
    
    -- Verify indexes were created
    ASSERT (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'conversation_threads') >= 6,
           'conversation_threads indexes not created';
    
    RAISE NOTICE '✅ All conversational chat tables and indexes created successfully!';
END $$;
