#!/usr/bin/env zsh
# -*- coding: utf-8 -*-
# scripts/test-all.sh
# --------------------------------------------------
# Executa testes de backend (pytest) e frontend (vitest) sequencialmente.
# --------------------------------------------------
set -euo pipefail

printf "\n🧪  Executando todos os testes...\n\n"

printf "🐍  Testes do backend...\n"
export PYTHONPATH="${PYTHONPATH:-}:$(pwd)/apps/backend"
pushd apps/backend >/dev/null
poetry run pytest -v
popd >/dev/null

printf "⚛️  Testes do frontend...\n"
pushd apps/frontend >/dev/null
npm test --silent
popd >/dev/null

printf "\n✅  Todos os testes concluídos!\n" 