#!/usr/bin/env zsh
# -*- coding: utf-8 -*-
# scripts/setup-zsh.sh
# --------------------------------------------------
# Configura o terminal para usar zsh de forma consistente
# e resolve problemas de configuração do shell
# --------------------------------------------------

set -euo pipefail

printf "\n🐚  Configurando terminal zsh para DataHero4...\n\n"

# Verifica se estamos usando zsh
if [[ "$SHELL" != "/bin/zsh" && "$SHELL" != "/opt/homebrew/bin/zsh" ]]; then
    printf "⚠️  Shell atual: $SHELL\n"
    printf "🔧  Mudando para zsh...\n"
    
    # Verifica se zsh está disponível
    if command -v zsh &> /dev/null; then
        ZSH_PATH=$(which zsh)
        printf "✅  zsh encontrado em: $ZSH_PATH\n"
        
        # Muda o shell padrão se necessário
        if [[ "$SHELL" != "$ZSH_PATH" ]]; then
            printf "🔄  Configurando zsh como shell padrão...\n"
            chsh -s "$ZSH_PATH" 2>/dev/null || {
                printf "❌  Não foi possível mudar o shell padrão automaticamente.\n"
                printf "💡  Execute manualmente: chsh -s $ZSH_PATH\n"
            }
        fi
        
        # Força o uso do zsh para esta sessão
        export SHELL="$ZSH_PATH"
        printf "✅  Shell configurado para esta sessão: $SHELL\n"
    else
        printf "❌  zsh não encontrado. Instale o zsh primeiro.\n"
        exit 1
    fi
else
    printf "✅  zsh já está configurado: $SHELL\n"
fi

# Verifica se estamos no diretório correto
if [[ ! -f "package.json" || ! -d "apps" ]]; then
    printf "❌  Execute este script a partir do diretório raiz do projeto DataHero4\n"
    printf "💡  Diretório atual: $(pwd)\n"
    printf "💡  Use: cd /Users/<USER>/coding-projects/datahero4 && ./scripts/setup-zsh.sh\n"
    exit 1
fi

printf "✅  Diretório correto: $(pwd)\n"

# Configura variáveis de ambiente para desenvolvimento
export PYTHONUNBUFFERED=1
export LOG_LEVEL=DEBUG
export UVICORN_LOG_LEVEL=debug

printf "✅  Variáveis de ambiente configuradas\n"

# Verifica dependências essenciais
printf "\n🔍  Verificando dependências...\n"

# Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    printf "✅  Node.js: $NODE_VERSION\n"
else
    printf "❌  Node.js não encontrado\n"
    exit 1
fi

# Poetry
if command -v poetry &> /dev/null; then
    POETRY_VERSION=$(poetry --version)
    printf "✅  Poetry: $POETRY_VERSION\n"
else
    printf "❌  Poetry não encontrado\n"
    exit 1
fi

# Git
if command -v git &> /dev/null; then
    GIT_VERSION=$(git --version)
    printf "✅  Git: $GIT_VERSION\n"
else
    printf "❌  Git não encontrado\n"
    exit 1
fi

printf "\n🎉  Terminal zsh configurado com sucesso!\n"
printf "💡  Agora você pode usar:\n"
printf "   • ./scripts/dev.sh - para desenvolvimento\n"
printf "   • ./scripts/test-all.sh - para testes\n"
printf "   • ./scripts/validate-deps.sh - para validar dependências\n\n"
