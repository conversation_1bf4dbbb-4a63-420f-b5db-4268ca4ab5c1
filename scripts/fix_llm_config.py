#!/usr/bin/env python3
"""
Quick fix to align LLM configuration with environment variables
Changes llm.yaml to use Groq instead of Fireworks
"""

import yaml
import os
import shutil
from datetime import datetime

def fix_llm_config():
    """Update llm.yaml to use Groq provider"""
    
    config_path = "apps/backend/src/config/setores/cambio/L2M/llm.yaml"
    
    # Backup existing config
    backup_path = f"{config_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy(config_path, backup_path)
    print(f"✅ Backup created: {backup_path}")
    
    # Load current config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Update all agents to use Groq
    groq_model = "llama3-70b-8192"  # Groq's Llama 3 70B model
    
    for agent_name, agent_config in config.items():
        if isinstance(agent_config, dict) and 'provider' in agent_config:
            print(f"📝 Updating {agent_name}:")
            print(f"   - Provider: {agent_config['provider']} → groq")
            print(f"   - Model: {agent_config['model']} → {groq_model}")
            
            agent_config['provider'] = 'groq'
            agent_config['model'] = groq_model
    
    # Save updated config
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False, sort_keys=False)
    
    print(f"\n✅ Config updated: {config_path}")
    print("\n💡 To revert: cp {backup_path} {config_path}")

if __name__ == "__main__":
    print("🔧 Fixing LLM Configuration")
    print("=" * 50)
    fix_llm_config()