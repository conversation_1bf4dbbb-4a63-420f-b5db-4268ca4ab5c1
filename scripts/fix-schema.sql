-- Fix Railway PostgreSQL schema - Add missing columns

-- Add missing patterns_extracted column to feedback_corrections
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'feedback_corrections' 
        AND column_name = 'patterns_extracted'
    ) THEN
        ALTER TABLE feedback_corrections 
        ADD COLUMN patterns_extracted JSON DEFAULT '[]'::json;
        RAISE NOTICE 'Added patterns_extracted column';
    ELSE
        RAISE NOTICE 'patterns_extracted column already exists';
    END IF;
EXCEPTION WHEN undefined_table THEN
    RAISE NOTICE 'feedback_corrections table does not exist - creating it';
    
    -- Create the full table if it doesn't exist
    CREATE TABLE feedback_corrections (
        id VARCHAR(100) PRIMARY KEY DEFAULT ('fb_' || to_char(NOW(), 'YYYYMMDDHH24MISS') || '_' || substr(md5(random()::text), 1, 8)),
        question TEXT NOT NULL,
        original_query TEXT NOT NULL DEFAULT '',
        corrected_query TEXT NOT NULL DEFAULT '',
        explanation TEXT,
        feedback_type VARCHAR(20) NOT NULL,
        category VARCHAR(50),
        user_comment TEXT,
        client_id VARCHAR(50) DEFAULT 'L2M',
        sector VARCHAR(50) DEFAULT 'cambio',
        user_id VARCHAR(50),
        patterns_extracted JSON DEFAULT '[]'::json,
        meta_data JSON,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        applied_count INTEGER DEFAULT 0,
        success_rate FLOAT DEFAULT 0.0,
        query_cache_id VARCHAR(100)
    );
    
    -- Create indexes
    CREATE INDEX idx_feedback_question ON feedback_corrections(question);
    CREATE INDEX idx_feedback_created_at ON feedback_corrections(created_at);
    CREATE INDEX idx_feedback_applied_count ON feedback_corrections(applied_count);
    
END $$;

-- Ensure query_cache table exists
CREATE TABLE IF NOT EXISTS query_cache (
    id VARCHAR(100) PRIMARY KEY DEFAULT ('qc_' || to_char(NOW(), 'YYYYMMDDHH24MISS') || '_' || substr(md5(random()::text), 1, 8)),
    question TEXT NOT NULL,
    sql_query TEXT NOT NULL,
    question_embedding BYTEA,
    sql_embedding BYTEA,
    cached_results JSON,
    business_analysis JSON,
    suggestions JSON,
    similarity_threshold FLOAT DEFAULT 0.85,
    client_id VARCHAR(50) DEFAULT 'L2M',
    sector VARCHAR(50) DEFAULT 'cambio',
    meta_data JSON,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    access_count INTEGER DEFAULT 1,
    last_accessed TIMESTAMP DEFAULT NOW(),
    cache_version VARCHAR(10) DEFAULT '1.0'
);

-- Create indexes for query_cache
CREATE INDEX IF NOT EXISTS idx_query_cache_question ON query_cache(question);
CREATE INDEX IF NOT EXISTS idx_query_cache_client_sector ON query_cache(client_id, sector);
CREATE INDEX IF NOT EXISTS idx_query_cache_created_at ON query_cache(created_at);
CREATE INDEX IF NOT EXISTS idx_query_cache_access_count ON query_cache(access_count);

-- Show current table structure
\d feedback_corrections;
\d query_cache;