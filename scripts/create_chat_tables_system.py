#!/usr/bin/env python3
"""
<PERSON>ript que usa o mesmo código do sistema para criar as tabelas de chat
"""
import os
import sys
import logging
from urllib.parse import urlparse

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_chat_tables():
    """
    Cria as tabelas de chat usando o mesmo método do sistema.
    """
    try:
        # Obter connection string do ambiente Railway (igual ao sistema)
        railway_db_url = os.getenv('DATABASE_URL_LEARNING')
        
        if not railway_db_url:
            logger.error("❌ DATABASE_URL_LEARNING não encontrada")
            return False
        
        logger.info(f"🔗 Usando Railway PostgreSQL: {railway_db_url[:50]}...")
        
        # Parse da URL (igual ao SimpleConversationDB)
        parsed = urlparse(railway_db_url)
        
        db_config = {
            'host': parsed.hostname,
            'port': parsed.port,
            'database': parsed.path[1:],  # Remove leading slash
            'user': parsed.username,
            'password': parsed.password
        }
        
        logger.info(f"📊 Conectando ao banco: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # Importar psycopg2 
        import psycopg2
        
        # Conectar ao banco
        conn = psycopg2.connect(**db_config)
        conn.autocommit = True
        cur = conn.cursor()
        
        logger.info("✅ Conexão estabelecida!")
        
        # SQL para criar tabelas (igual ao sistema)
        sql = """
        -- Criar tabelas para o sistema de chat avançado
        CREATE TABLE IF NOT EXISTS conversation_threads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            sector VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_message_at TIMESTAMP,
            message_count INTEGER DEFAULT 0,
            total_tokens_used INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT true,
            metadata JSONB DEFAULT '{}'
        );

        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
            role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tokens_used INTEGER DEFAULT 0,
            metadata JSONB DEFAULT '{}'
        );

        -- Índices para performance
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_client 
            ON conversation_threads(user_id, client_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_active 
            ON conversation_threads(is_active);
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_created_at 
            ON conversation_threads(created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_id 
            ON conversation_messages(thread_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at 
            ON conversation_messages(created_at DESC);
        """
        
        logger.info("📝 Executando SQL para criar tabelas...")
        cur.execute(sql)
        
        logger.info("✅ Tabelas criadas com sucesso!")
        
        # Verificar tabelas criadas
        cur.execute("SELECT COUNT(*) FROM conversation_threads;")
        result = cur.fetchone()
        count = result[0] if result else 0
        logger.info(f"📊 Tabela conversation_threads: {count} registros")
        
        cur.execute("SELECT COUNT(*) FROM conversation_messages;")
        result = cur.fetchone()
        count = result[0] if result else 0
        logger.info(f"📊 Tabela conversation_messages: {count} registros")
        
        # Fechar conexão
        conn.close()
        logger.info("🔒 Conexão fechada")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro ao criar tabelas: {e}")
        return False

if __name__ == "__main__":
    success = create_chat_tables()
    sys.exit(0 if success else 1) 