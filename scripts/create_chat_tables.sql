-- <PERSON><PERSON>r tabelas para o sistema de chat avançado
-- DataHero4 - Chat Conversacional

-- Tabela de threads de conversa
CREATE TABLE IF NOT EXISTS conversation_threads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    sector VARCHAR(255) NOT NULL,
    title VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_message_at TIMESTAMP,
    message_count INTEGER DEFAULT 0,
    total_tokens_used INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}'
);

-- Ta<PERSON><PERSON> de mensagens
CREATE TABLE IF NOT EXISTS conversation_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tokens_used INTEGER DEFAULT 0,
    processing_time FLOAT DEFAULT 0,
    analysis_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}'
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_id ON conversation_threads(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_client_id ON conversation_threads(client_id);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_sector ON conversation_threads(sector);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_created_at ON conversation_threads(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_threads_is_active ON conversation_threads(is_active);

CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_id ON conversation_messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_role ON conversation_messages(role);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at);

-- Trigger para atualizar updated_at em threads
CREATE OR REPLACE FUNCTION update_thread_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_thread_updated_at
    BEFORE UPDATE ON conversation_threads
    FOR EACH ROW
    EXECUTE FUNCTION update_thread_updated_at();

-- Trigger para atualizar contadores em threads quando mensagens são adicionadas
CREATE OR REPLACE FUNCTION update_thread_counters()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE conversation_threads 
        SET 
            message_count = message_count + 1,
            last_message_at = NEW.created_at,
            total_tokens_used = total_tokens_used + COALESCE(NEW.tokens_used, 0),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.thread_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE conversation_threads 
        SET 
            message_count = message_count - 1,
            total_tokens_used = total_tokens_used - COALESCE(OLD.tokens_used, 0),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = OLD.thread_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_thread_counters
    AFTER INSERT OR DELETE ON conversation_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_thread_counters();

-- Comentários das tabelas
COMMENT ON TABLE conversation_threads IS 'Tabela de threads de conversa para o chat avançado';
COMMENT ON TABLE conversation_messages IS 'Tabela de mensagens das conversas';

-- Inserir dados de exemplo (opcional)
INSERT INTO conversation_threads (user_id, client_id, sector, title, description)
VALUES 
    ('default-user', 'L2M', 'cambio', 'Exemplo de Conversa', 'Thread de exemplo para testes')
ON CONFLICT DO NOTHING;

SELECT 'Tabelas criadas com sucesso!' AS resultado; 