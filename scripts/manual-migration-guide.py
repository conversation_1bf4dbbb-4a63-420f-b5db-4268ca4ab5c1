#!/usr/bin/env python3
"""
Manual Migration Guide - Supabase to Railway PostgreSQL
Executa exportação do Supabase e gera instruções para Railway
"""

import os
import sys
import json
import psycopg2
from typing import Dict, Any

# Cores para output
class Colors:
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    RED = '\033[0;31m'
    NC = '\033[0m'

def print_status(msg: str):
    print(f"{Colors.BLUE}ℹ️  {msg}{Colors.NC}")

def print_success(msg: str):
    print(f"{Colors.GREEN}✅ {msg}{Colors.NC}")

def print_warning(msg: str):
    print(f"{Colors.YELLOW}⚠️  {msg}{Colors.NC}")

def print_error(msg: str):
    print(f"{Colors.RED}❌ {msg}{Colors.NC}")

def export_supabase_data():
    """Exporta dados do Supabase usando credenciais do .env"""
    print_status("Conectando ao Supabase...")
    
    # Credenciais do .env
    supabase_config = {
        "host": "aws-0-us-west-1.pooler.supabase.com",
        "port": 5432,
        "database": "postgres", 
        "user": "postgres.ftcdeahipfoensjlaziz",
        "password": "EqVXrBiWZVXMU64p"
    }
    
    try:
        # Conectar ao Supabase
        conn = psycopg2.connect(**supabase_config)
        cursor = conn.cursor()
        
        print_success("Conectado ao Supabase")
        
        # Verificar tabelas existentes
        cursor.execute("""
            SELECT table_name, 
                   (SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_name = t.table_name AND table_schema = 'public') as column_count
            FROM information_schema.tables t
            WHERE table_schema = 'public' 
            AND table_name IN ('query_cache', 'feedback_corrections', 'query_history', 'conversation_threads', 'conversation_messages')
            ORDER BY table_name;
        """)
        
        tables_info = cursor.fetchall()
        
        if not tables_info:
            print_warning("Nenhuma tabela de learning encontrada no Supabase")
            print_warning("Isso é normal para uma instalação nova")
            return None
        
        print_success("Tabelas encontradas no Supabase:")
        for table_name, col_count in tables_info:
            print(f"  📋 {table_name} ({col_count} colunas)")
        
        # Contar registros em cada tabela
        table_stats = {}
        for table_name, _ in tables_info:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            table_stats[table_name] = count
            print(f"    └─ {count} registros")
        
        # Exportar dados se houver registros
        export_data = {}
        total_records = sum(table_stats.values())
        
        if total_records > 0:
            print_status(f"Exportando {total_records} registros totais...")
            
            for table_name in table_stats.keys():
                if table_stats[table_name] > 0:
                    cursor.execute(f"SELECT * FROM {table_name}")
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    
                    export_data[table_name] = {
                        "columns": columns,
                        "rows": [list(row) for row in rows],
                        "count": len(rows)
                    }
                    print_success(f"  ✓ {table_name}: {len(rows)} registros")
            
            # Salvar dados
            with open("supabase_export.json", "w") as f:
                json.dump(export_data, f, indent=2, default=str)
            
            print_success("Dados exportados para: supabase_export.json")
        else:
            print_warning("Nenhum registro encontrado para exportar")
            export_data = {}
        
        # Salvar estatísticas
        stats = {
            "export_timestamp": str(psycopg2.TimestampFromTicks(psycopg2.time.time())),
            "source_database": f"{supabase_config['host']}:{supabase_config['port']}/{supabase_config['database']}",
            "tables": table_stats,
            "total_records": total_records
        }
        
        with open("export_stats.json", "w") as f:
            json.dump(stats, f, indent=2)
        
        cursor.close()
        conn.close()
        
        return export_data
        
    except Exception as e:
        print_error(f"Erro ao conectar/exportar Supabase: {e}")
        return None

def generate_railway_instructions(export_data: Dict):
    """Gera instruções detalhadas para configurar Railway"""
    
    instructions = """
🚂 INSTRUÇÕES PARA RAILWAY POSTGRESQL
=====================================

Dados do Supabase exportados com sucesso! 
Agora siga estas etapas para configurar Railway:

📋 PASSO 1: Criar PostgreSQL no Railway Dashboard
-----------------------------------------------
1. Acesse: https://railway.app/dashboard
2. Selecione seu projeto DataHero4
3. Clique em "Add Service" → "Database" → "PostgreSQL"
4. Aguarde o PostgreSQL ser criado (30-60 segundos)

🔗 PASSO 2: Conectar PostgreSQL ao Backend
-----------------------------------------
1. Clique no serviço Backend
2. Vá para aba "Variables"
3. Clique em "Connect" → Selecione o serviço PostgreSQL
4. Conecte a variável: DATABASE_URL_LEARNING

⚙️ PASSO 3: Configurar Variáveis de Cache
----------------------------------------
No serviço Backend, adicione estas variáveis:

ENABLE_L1_CACHE=true
CACHE_L1_MAX_SIZE=50
CACHE_L1_TTL=300
CACHE_L2_TTL=3600
LOG_LEVEL=INFO
ENVIRONMENT=production

📊 PASSO 4: Criar Estrutura de Tabelas
------------------------------------
1. Vá para o serviço PostgreSQL
2. Clique em "Connect" para abrir terminal
3. Execute este comando:

\\copy (SELECT script FROM migrations) TO STDOUT;

Ou execute manualmente:
"""

    if export_data:
        total_records = sum(table['count'] for table in export_data.values())
        instructions += f"""
📥 PASSO 5: Importar Dados ({total_records} registros)
------------------------------------------------
1. No terminal PostgreSQL do Railway, execute:

\\i /path/to/supabase_migration.sql

2. Para cada tabela com dados:
"""
        
        for table_name, table_data in export_data.items():
            count = table_data['count']
            instructions += f"""
   {table_name} ({count} registros):
   - Copie dados de supabase_export.json
   - Use INSERT statements ou \\copy
"""
    
    instructions += """

🧪 PASSO 6: Testar Configuração
------------------------------
Execute no seu terminal local:

cd /Users/<USER>/Coding/datahero4/apps/backend
poetry run python src/main.py "teste" --client-id L2M --sector cambio

Procure por: "🚄 Using Railway PostgreSQL for learning database"

📈 PERFORMANCE ESPERADA
======================
- Cache L1: <1ms (memória)
- Cache L2: <50ms (Redis Railway) 
- Cache L3: <100ms (PostgreSQL Railway)
- Melhoria total: 98% vs Supabase (7000ms → <100ms)

🆘 TROUBLESHOOTING
=================
Se o sistema ainda usar Supabase:
1. Verifique se DATABASE_URL_LEARNING está definida
2. Verifique logs: railway logs --service backend
3. Reinicie o serviço backend

✅ SUCESSO!
===========
Quando ver "🚄 Using Railway PostgreSQL" nos logs,
a migração está completa e funcionando!
"""
    
    with open("railway_migration_instructions.txt", "w") as f:
        f.write(instructions)
    
    print_success("Instruções salvas em: railway_migration_instructions.txt")
    print(instructions)

def generate_sql_import_script(export_data: Dict):
    """Gera script SQL para importar dados"""
    if not export_data:
        return
    
    sql_lines = [
        "-- Script de importação de dados do Supabase",
        "-- Execute este script no PostgreSQL Railway",
        "",
        "-- Primeiro, execute o script de estrutura:",
        "-- \\i supabase_migration.sql",
        "",
        "-- Depois, importe os dados:",
        ""
    ]
    
    for table_name, table_data in export_data.items():
        columns = table_data['columns']
        rows = table_data['rows']
        
        if not rows:
            continue
        
        sql_lines.append(f"-- Dados da tabela {table_name} ({len(rows)} registros)")
        
        for row in rows[:5]:  # Mostrar apenas primeiras 5 linhas como exemplo
            # Escapar valores
            escaped_values = []
            for value in row:
                if value is None:
                    escaped_values.append("NULL")
                elif isinstance(value, str):
                    escaped_values.append(f"'{value.replace(chr(39), chr(39)+chr(39))}'")
                else:
                    escaped_values.append(str(value))
            
            insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(escaped_values)});"
            sql_lines.append(insert_sql)
        
        if len(rows) > 5:
            sql_lines.append(f"-- ... mais {len(rows) - 5} registros em supabase_export.json")
        
        sql_lines.append("")
    
    sql_content = "\n".join(sql_lines)
    
    with open("import_data.sql", "w") as f:
        f.write(sql_content)
    
    print_success("Script SQL gerado: import_data.sql")

def main():
    """Função principal"""
    print_status("🔄 DataHero4 - Exportação Supabase e Guia Railway")
    print_status("=" * 50)
    
    # Exportar dados do Supabase
    export_data = export_supabase_data()
    
    # Gerar instruções para Railway
    generate_railway_instructions(export_data)
    
    # Gerar script SQL de importação
    if export_data:
        generate_sql_import_script(export_data)
    
    print_success("=" * 50)
    print_success("🎉 EXPORTAÇÃO COMPLETA!")
    print_status("Arquivos gerados:")
    
    files = []
    if os.path.exists("supabase_export.json"):
        files.append("📄 supabase_export.json - Dados exportados")
    if os.path.exists("export_stats.json"):
        files.append("📊 export_stats.json - Estatísticas")
    if os.path.exists("railway_migration_instructions.txt"):
        files.append("📋 railway_migration_instructions.txt - Instruções")
    if os.path.exists("import_data.sql"):
        files.append("📜 import_data.sql - Script de importação")
    
    for file in files:
        print(f"  {file}")
    
    print_status("")
    print_status("Próximo passo:")
    print_status("1. Abra railway_migration_instructions.txt")
    print_status("2. Siga as instruções passo a passo")
    print_status("3. Configure PostgreSQL no Railway Dashboard")
    
    return True

if __name__ == "__main__":
    # Carregar .env se possível
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    success = main()
    sys.exit(0 if success else 1)