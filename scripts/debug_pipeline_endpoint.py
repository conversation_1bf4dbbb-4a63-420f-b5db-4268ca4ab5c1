"""
Temporary debug endpoint to add to api.py for pipeline investigation
"""

# Add this to api.py temporarily

@app.post("/debug-pipeline")
async def debug_pipeline(request: AskRequest):
    """Debug endpoint to investigate pipeline flow."""
    logger.info(f"🔥 [DEBUG] ===== DEBUG PIPELINE REQUEST =====")
    logger.info(f"🔥 [DEBUG] Question: '{request.question}'")
    logger.info(f"🔥 [DEBUG] Client: {request.client_id}, Sector: {request.sector}")
    
    try:
        # Build state
        logger.info(f"🔥 [DEBUG] Building initial state...")
        state = build_initial_state(request)
        query_id = f"DEBUG_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        state["query_id"] = query_id
        state["analysis_level"] = "direct_answer"
        
        logger.info(f"🔥 [DEBUG] State built with keys: {list(state.keys())}")
        
        # Check LLM configuration
        try:
            llm_config_path = state.get("llm_config_path")
            logger.info(f"🔥 [DEBUG] LLM config path: {llm_config_path}")
            
            if llm_config_path and os.path.exists(llm_config_path):
                with open(llm_config_path, 'r') as f:
                    import yaml
                    llm_config = yaml.safe_load(f)
                    logger.info(f"🔥 [DEBUG] LLM config loaded: {list(llm_config.keys())}")
                    query_gen_config = llm_config.get('query_generator_agent', {})
                    logger.info(f"🔥 [DEBUG] Query generator provider: {query_gen_config.get('provider')}")
                    logger.info(f"🔥 [DEBUG] Query generator model: {query_gen_config.get('model')}")
            else:
                logger.error(f"🔥 [DEBUG] LLM config file not found: {llm_config_path}")
        except Exception as e:
            logger.error(f"🔥 [DEBUG] LLM config load error: {e}")
        
        # Check environment variables
        logger.info(f"🔥 [DEBUG] Environment vars:")
        logger.info(f"🔥 [DEBUG] - DATAHERO_LLM_PROVIDER: {os.getenv('DATAHERO_LLM_PROVIDER')}")
        logger.info(f"🔥 [DEBUG] - FIREWORKS_API_KEY: {bool(os.getenv('FIREWORKS_API_KEY'))}")
        logger.info(f"🔥 [DEBUG] - GROQ_API_KEY: {bool(os.getenv('GROQ_API_KEY'))}")
        logger.info(f"🔥 [DEBUG] - ENVIRONMENT: {os.getenv('ENVIRONMENT')}")
        
        # Test LLM provider directly
        try:
            from src.utils.llm_client_manager import get_llm_client_manager
            llm_manager = get_llm_client_manager()
            logger.info(f"🔥 [DEBUG] LLM manager initialized: {bool(llm_manager)}")
        except Exception as e:
            logger.error(f"🔥 [DEBUG] LLM manager error: {e}")
        
        # Try to execute just the first node
        try:
            logger.info(f"🔥 [DEBUG] Testing workflow initialization...")
            graph = get_graph()
            logger.info(f"🔥 [DEBUG] Graph obtained: {bool(graph)}")
            
            # Execute just the first step
            logger.info(f"🔥 [DEBUG] Executing workflow...")
            final_state = graph.invoke(state, config={"thread_id": state["thread_id"]})
            
            logger.info(f"🔥 [DEBUG] Workflow completed. Final state keys: {list(final_state.keys())}")
            logger.info(f"🔥 [DEBUG] SQL Query: {final_state.get('sql_query')}")
            logger.info(f"🔥 [DEBUG] Business Analysis: {bool(final_state.get('business_analysis'))}")
            
        except Exception as e:
            logger.error(f"🔥 [DEBUG] Workflow execution error: {e}")
            import traceback
            logger.error(f"🔥 [DEBUG] Traceback: {traceback.format_exc()}")
        
        return {
            "debug": "Pipeline debug completed",
            "query_id": query_id,
            "state_keys": list(state.keys()),
            "llm_config_path": state.get("llm_config_path"),
            "environment": os.getenv('ENVIRONMENT'),
            "datahero_llm_provider": os.getenv('DATAHERO_LLM_PROVIDER')
        }
        
    except Exception as e:
        logger.error(f"🔥 [DEBUG] Debug endpoint error: {e}")
        import traceback
        logger.error(f"🔥 [DEBUG] Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Debug error: {e}")