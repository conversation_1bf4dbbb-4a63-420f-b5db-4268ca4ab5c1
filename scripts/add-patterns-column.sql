-- Add missing patterns_extracted column to feedback_corrections table

-- Check if column exists and add it if missing
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'feedback_corrections' 
        AND column_name = 'patterns_extracted'
    ) THEN
        ALTER TABLE feedback_corrections 
        ADD COLUMN patterns_extracted JSON DEFAULT '[]'::json;
        RAISE NOTICE 'Added patterns_extracted column to feedback_corrections table';
    ELSE
        RAISE NOTICE 'patterns_extracted column already exists';
    END IF;
END $$;

-- Verify the table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'feedback_corrections'
ORDER BY ordinal_position;