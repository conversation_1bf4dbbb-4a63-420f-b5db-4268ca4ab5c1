#!/usr/bin/env python3
"""
Fix Query History Schema - Add Missing Columns
==============================================

Adds the missing 'has_feedback' column to the query_history table.
"""

import os
import psycopg2
from psycopg2 import sql
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_url():
    """Get database URL from environment variables."""
    # Try Railway environment variables first
    railway_url = os.getenv('DATABASE_URL')
    if railway_url:
        return railway_url
    
    # Fallback to individual components
    host = os.getenv('PGHOST', 'localhost')
    port = os.getenv('PGPORT', '5432')
    database = os.getenv('PGDATABASE', 'datahero4')
    user = os.getenv('PGUSER', 'postgres')
    password = os.getenv('PGPASSWORD', '')
    
    return f"postgresql://{user}:{password}@{host}:{port}/{database}"

def fix_query_history_schema():
    """Add missing columns to query_history table."""
    database_url = get_database_url()
    
    try:
        # Connect to database
        logger.info("🔗 Connecting to database...")
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # Check if query_history table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'query_history'
            )
        """)
        
        if not cursor.fetchone()[0]:
            logger.info("📋 query_history table doesn't exist, creating it...")
            
            # Create query_history table with all required columns
            create_table_sql = """
            CREATE TABLE query_history (
                id VARCHAR(255) PRIMARY KEY,
                question TEXT NOT NULL,
                sql_query TEXT,
                result_count INTEGER DEFAULT 0,
                success BOOLEAN DEFAULT false,
                error_message TEXT,
                has_feedback BOOLEAN DEFAULT false,
                feedback_type VARCHAR(50),
                feedback_explanation TEXT,
                corrected_query TEXT,
                user_id VARCHAR(255),
                session_id VARCHAR(255),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                meta_data JSONB DEFAULT '{}'
            )
            """
            
            cursor.execute(create_table_sql)
            logger.info("✅ query_history table created successfully")
            
        else:
            logger.info("📋 query_history table exists, checking for missing columns...")
            
            # Check if has_feedback column exists
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'query_history' 
                AND column_name = 'has_feedback'
            """)
            
            if not cursor.fetchone():
                logger.info("➕ Adding has_feedback column...")
                cursor.execute("""
                    ALTER TABLE query_history 
                    ADD COLUMN has_feedback BOOLEAN DEFAULT false
                """)
                logger.info("✅ has_feedback column added successfully")
            else:
                logger.info("✅ has_feedback column already exists")
        
        # Check if conversation_threads table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'conversation_threads'
            )
        """)
        
        if not cursor.fetchone()[0]:
            logger.info("📋 conversation_threads table doesn't exist, creating it...")
            
            # Create conversation_threads table
            create_threads_sql = """
            CREATE TABLE conversation_threads (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id VARCHAR(255) NOT NULL,
                client_id VARCHAR(255) NOT NULL,
                sector VARCHAR(255) NOT NULL,
                title VARCHAR(255),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_message_at TIMESTAMP,
                message_count INTEGER DEFAULT 0,
                total_tokens_used INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT true,
                metadata JSONB DEFAULT '{}'
            )
            """
            
            cursor.execute(create_threads_sql)
            logger.info("✅ conversation_threads table created successfully")
            
            # Create conversation_messages table
            create_messages_sql = """
            CREATE TABLE conversation_messages (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
                role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
                content TEXT NOT NULL,
                tokens_used INTEGER DEFAULT 0,
                model_used VARCHAR(100),
                response_time_ms INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata JSONB DEFAULT '{}'
            )
            """
            
            cursor.execute(create_messages_sql)
            logger.info("✅ conversation_messages table created successfully")
            
            # Create indexes
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_conversation_threads_client_sector 
                ON conversation_threads(client_id, sector)
            """)
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_conversation_threads_user 
                ON conversation_threads(user_id)
            """)
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread 
                ON conversation_messages(thread_id)
            """)
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_conversation_messages_created 
                ON conversation_messages(created_at)
            """)
            
            logger.info("✅ Database indexes created successfully")
            
        else:
            logger.info("✅ conversation_threads table already exists")
        
        # Commit changes
        conn.commit()
        logger.info("🎉 All database schema fixes completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Database schema fix failed: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
    
    return True

if __name__ == "__main__":
    logger.info("🚄 Database Schema Fix Tool")
    logger.info("=" * 40)
    
    if fix_query_history_schema():
        logger.info("✅ Schema fix completed successfully!")
    else:
        logger.error("❌ Schema fix failed!")