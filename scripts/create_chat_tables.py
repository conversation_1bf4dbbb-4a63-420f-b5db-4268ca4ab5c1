#!/usr/bin/env python3
"""
Script para criar tabelas de chat no PostgreSQL do Railway
DataHero4 - Chat Conversacional
"""

import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys
from pathlib import Path

def get_database_url():
    """Obtém a URL do banco de dados das variáveis de ambiente"""
    db_url = os.getenv('DATABASE_URL')
    if not db_url:
        print("❌ Erro: DATABASE_URL não encontrada nas variáveis de ambiente")
        sys.exit(1)
    return db_url

def read_sql_file():
    """Lê o arquivo SQL com as definições das tabelas"""
    sql_file = Path(__file__).parent / 'create_chat_tables.sql'
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ Erro: Arquivo {sql_file} não encontrado")
        sys.exit(1)

def main():
    """Função principal"""
    print("🚀 Criando tabelas de chat no PostgreSQL do Railway...")
    
    # Obter URL do banco
    db_url = get_database_url()
    print(f"📊 Conectando ao banco: {db_url[:50]}...")
    
    # Ler SQL
    sql_content = read_sql_file()
    
    try:
        # Conectar ao banco
        conn = psycopg2.connect(db_url)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        # Criar cursor
        cur = conn.cursor()
        
        # Executar SQL
        print("📝 Executando SQL...")
        cur.execute(sql_content)
        
        # Verificar se as tabelas foram criadas
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('conversation_threads', 'conversation_messages')
            ORDER BY table_name;
        """)
        
        tables = cur.fetchall()
        print(f"✅ Tabelas criadas com sucesso: {[t[0] for t in tables]}")
        
        # Verificar se há registros de exemplo
        cur.execute("SELECT COUNT(*) FROM conversation_threads;")
        result = cur.fetchone()
        thread_count = result[0] if result else 0
        print(f"📊 Threads de exemplo criadas: {thread_count}")
        
        # Verificar índices
        cur.execute("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename IN ('conversation_threads', 'conversation_messages')
            AND indexname LIKE 'idx_%'
            ORDER BY indexname;
        """)
        
        indexes = cur.fetchall()
        print(f"🔍 Índices criados: {len(indexes)}")
        
        # Fechar conexões
        cur.close()
        conn.close()
        
        print("\n🎉 **SUCESSO**: Tabelas de chat criadas com sucesso!")
        print("📋 Próximos passos:")
        print("   1. Testar criação de threads: POST /v1/chat/threads")
        print("   2. Testar sistema de chat avançado no frontend")
        print("   3. Verificar se pergunta não trava mais")
        
    except Exception as e:
        print(f"❌ Erro ao criar tabelas: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 