-- Complete schema fix for Railway PostgreSQL learning tables

-- Add missing query_cache_id column to feedback_corrections
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'feedback_corrections' 
        AND column_name = 'query_cache_id'
    ) THEN
        ALTER TABLE feedback_corrections 
        ADD COLUMN query_cache_id VARCHAR(100);
        RAISE NOTICE 'Added query_cache_id column to feedback_corrections table';
    ELSE
        RAISE NOTICE 'query_cache_id column already exists';
    END IF;
END $$;

-- Ensure query_cache table exists with all required columns
CREATE TABLE IF NOT EXISTS query_cache (
    id VARCHAR(100) PRIMARY KEY DEFAULT ('qc_' || to_char(NOW(), 'YYYYMMDDHH24MISS') || '_' || substr(md5(random()::text), 1, 8)),
    question TEXT NOT NULL,
    sql_query TEXT NOT NULL,
    question_embedding BYTEA,
    sql_embedding BYTEA,
    cached_results JSON,
    business_analysis JSON,
    suggestions JSON,
    similarity_threshold FLOAT DEFAULT 0.85,
    client_id VARCHAR(50) DEFAULT 'L2M',
    sector VARCHAR(50) DEFAULT 'cambio',
    meta_data JSON,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    access_count INTEGER DEFAULT 1,
    last_accessed TIMESTAMP DEFAULT NOW(),
    cache_version VARCHAR(10) DEFAULT '1.0'
);

-- Create indexes for query_cache if they don't exist
CREATE INDEX IF NOT EXISTS idx_query_cache_question ON query_cache(question);
CREATE INDEX IF NOT EXISTS idx_query_cache_client_sector ON query_cache(client_id, sector);
CREATE INDEX IF NOT EXISTS idx_query_cache_created_at ON query_cache(created_at);
CREATE INDEX IF NOT EXISTS idx_query_cache_access_count ON query_cache(access_count);

-- Add foreign key constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_feedback_query_cache'
        AND table_name = 'feedback_corrections'
    ) THEN
        ALTER TABLE feedback_corrections 
        ADD CONSTRAINT fk_feedback_query_cache 
        FOREIGN KEY (query_cache_id) REFERENCES query_cache(id);
        RAISE NOTICE 'Added foreign key constraint fk_feedback_query_cache';
    ELSE
        RAISE NOTICE 'Foreign key constraint already exists';
    END IF;
EXCEPTION WHEN others THEN
    RAISE NOTICE 'Could not add foreign key constraint (table might not exist yet)';
END $$;

-- Final verification - show table structures
\echo '=== FEEDBACK_CORRECTIONS TABLE STRUCTURE ==='
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'feedback_corrections'
ORDER BY ordinal_position;

\echo '=== QUERY_CACHE TABLE STRUCTURE ==='
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'query_cache'
ORDER BY ordinal_position;

\echo '=== TABLE COUNT VERIFICATION ==='
SELECT 
  'feedback_corrections' as table_name,
  COUNT(*) as row_count
FROM feedback_corrections
UNION ALL
SELECT 
  'query_cache' as table_name,
  COUNT(*) as row_count
FROM query_cache;