#!/usr/bin/env python3
"""
Script final para criar tabelas de chat no PostgreSQL Railway
"""
import psycopg2
import sys

# Connection string Railway PostgreSQL público
DATABASE_URL = "postgresql://postgres:<EMAIL>:40697/railway"

def create_tables():
    conn = None
    try:
        print("🔗 Conectando ao banco Railway...")
        
        # Conectar ao banco
        conn = psycopg2.connect(DATABASE_URL)
        conn.autocommit = True
        cur = conn.cursor()
        
        print("✅ Conexão estabelecida!")
        
        # Criar tabelas
        sql = """
        -- Criar tabelas para o sistema de chat avançado
        CREATE TABLE IF NOT EXISTS conversation_threads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            sector VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_message_at TIMESTAMP,
            message_count INTEGER DEFAULT 0,
            total_tokens_used INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT true,
            metadata JSONB DEFAULT '{}'
        );

        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
            role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tokens_used INTEGER DEFAULT 0,
            metadata JSONB DEFAULT '{}'
        );

        -- Índices para performance
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_user_client 
            ON conversation_threads(user_id, client_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_active 
            ON conversation_threads(is_active);
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_created_at 
            ON conversation_threads(created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread_id 
            ON conversation_messages(thread_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at 
            ON conversation_messages(created_at DESC);
        """
        
        print("📝 Executando SQL para criar tabelas...")
        cur.execute(sql)
        
        print("✅ Tabelas criadas com sucesso!")
        
        # Verificar tabelas criadas
        cur.execute("SELECT COUNT(*) FROM conversation_threads;")
        result = cur.fetchone()
        count = result[0] if result else 0
        print(f"📊 Tabela conversation_threads: {count} registros")
        
        cur.execute("SELECT COUNT(*) FROM conversation_messages;")
        result = cur.fetchone()
        count = result[0] if result else 0
        print(f"📊 Tabela conversation_messages: {count} registros")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar tabelas: {e}")
        return False
    finally:
        if conn:
            conn.close()
            print("🔒 Conexão fechada")

if __name__ == "__main__":
    success = create_tables()
    sys.exit(0 if success else 1) 