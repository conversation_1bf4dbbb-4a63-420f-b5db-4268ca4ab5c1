#!/usr/bin/env python3
"""
Debug específico para aplicação de padrões de aprendizado.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'apps', 'backend'))

from src.utils.learning_db_utils import get_db_manager
from src.learning.feedback_pattern_analyzer import FeedbackPatternAnalyzer

def debug_pattern_application():
    """Debug detalhado da aplicação de padrões."""
    print("🔍 DEBUG: Aplicação de Padrões de Aprendizado")
    print("=" * 60)
    
    # Inicializar sistema
    db_manager = get_db_manager()
    analyzer = FeedbackPatternAnalyzer(db_manager)
    
    # Analisar feedbacks
    print("📊 1. Analisando feedbacks...")
    analysis = analyzer.analyze_recent_feedbacks(days=30)
    
    if analysis["status"] != "success":
        print(f"❌ Falha na análise: {analysis.get('message')}")
        return
    
    print(f"✅ Análise concluída: {len(analysis['patterns'])} padrões encontrados")
    
    # Mostrar padrões detalhadamente
    print(f"\n🎯 2. Padrões identificados:")
    for i, pattern in enumerate(analysis['patterns'], 1):
        print(f"\n   Padrão {i}:")
        print(f"     ID: {pattern['pattern_id']}")
        print(f"     Tipo: {pattern['pattern_type']}")
        print(f"     Trigger Keywords: {pattern['trigger_keywords']}")
        print(f"     SQL Pattern Original: '{pattern['original_sql_pattern']}'")
        print(f"     SQL Pattern Corrigido: '{pattern['corrected_sql_pattern']}'")
        print(f"     Confiança: {pattern['confidence']}")
        print(f"     Descrição: {pattern['description']}")
    
    # Teste de aplicação
    test_cases = [
        {
            "question": "mostre boletas de janeiro 2025",
            "sql": "SELECT * FROM boleta WHERE data_criacao = '2024-01-01'",
            "description": "Teste com ano 2024 no SQL"
        },
        {
            "question": "boletas do ano 2024",
            "sql": "SELECT * FROM boleta WHERE EXTRACT(YEAR FROM data_operacao) = 2024",
            "description": "Teste com ano 2024 em EXTRACT"
        },
        {
            "question": "dados de 2024",
            "sql": "SELECT * FROM boleta WHERE ano = 2024",
            "description": "Teste com ano 2024 em coluna ano"
        }
    ]
    
    print(f"\n🧪 3. Testando aplicação de padrões:")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n   Teste {i}: {test_case['description']}")
        print(f"     Pergunta: {test_case['question']}")
        print(f"     SQL: {test_case['sql']}")
        
        # Buscar padrões aplicáveis
        applicable_patterns = analyzer.get_applicable_patterns(test_case['question'], test_case['sql'])
        print(f"     Padrões aplicáveis: {len(applicable_patterns)}")
        
        if applicable_patterns:
            for j, pattern in enumerate(applicable_patterns, 1):
                print(f"       Padrão {j}: {pattern.description}")
                print(f"         Trigger: {pattern.trigger_keywords}")
                print(f"         SQL Pattern: '{pattern.original_sql_pattern}'")
                print(f"         Confiança: {pattern.confidence}")
                
                # Debug da verificação de trigger keywords
                question_lower = test_case['question'].lower()
                keywords_found = [kw for kw in pattern.trigger_keywords if kw in question_lower]
                print(f"         Keywords encontradas: {keywords_found}")
                
                # Debug da verificação de SQL pattern
                import re
                sql_match = re.search(pattern.original_sql_pattern, test_case['sql'], re.IGNORECASE)
                print(f"         SQL match: {bool(sql_match)}")
                if sql_match:
                    print(f"         SQL match groups: {sql_match.groups()}")
            
            # Aplicar padrões
            corrected_sql, corrections = analyzer.apply_patterns_to_sql(test_case['sql'], applicable_patterns)
            print(f"     SQL corrigido: {corrected_sql}")
            print(f"     Correções aplicadas: {corrections}")
            
        else:
            print(f"     ❌ Nenhum padrão aplicável encontrado")
            
            # Debug por que não foi encontrado
            print(f"     🔍 Debug:")
            question_lower = test_case['question'].lower()
            sql_lower = test_case['sql'].lower()
            
            for pattern in analysis['patterns']:
                print(f"       Padrão: {pattern['description']}")
                
                # Verificar keywords
                keywords_in_question = [kw for kw in pattern['trigger_keywords'] if kw in question_lower]
                print(f"         Keywords na pergunta: {keywords_in_question}")
                
                # Verificar SQL pattern
                if pattern['original_sql_pattern']:
                    import re
                    sql_match = re.search(pattern['original_sql_pattern'], test_case['sql'], re.IGNORECASE)
                    print(f"         SQL pattern match: {bool(sql_match)}")
                else:
                    print(f"         SQL pattern vazio")
    
    print(f"\n🏁 Debug concluído!")

if __name__ == "__main__":
    debug_pattern_application()
