#!/usr/bin/env python3
"""
Script para criar todas as tabelas necessárias para KPIs no PostgreSQL.
Inteligente: Railway = learning DB, Local = main DB como fallback.
"""

import os
import sys
import logging
import psycopg2
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    logging.info("✅ Environment variables loaded from .env")
except ImportError:
    logging.warning("⚠️ python-dotenv not available, using system environment only")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_smart_db_connection():
    """Get smart PostgreSQL connection based on environment."""
    try:
        # Detect Railway environment
        is_railway = os.getenv('RAILWAY_ENVIRONMENT') is not None
        
        if is_railway:
            # Use Railway PostgreSQL learning database
            database_url = os.getenv('DATABASE_URL_LEARNING')
            if not database_url:
                logger.error("❌ DATABASE_URL_LEARNING not found in Railway environment")
                return None
            logger.info("🚄 Railway environment: using learning database")
        else:
            # Use main database as fallback for local development
            database_url = os.getenv('DATABASE_URL')
            if not database_url:
                logger.error("❌ DATABASE_URL not found for local environment")
                return None
            logger.info("💻 Local environment: using main database as fallback")
            
        conn = psycopg2.connect(database_url)
        logger.info(f"✅ Connected to PostgreSQL ({database_url[:50]}...)")
        return conn
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to PostgreSQL: {e}")
        return None

def create_kpi_definitions_table(cursor):
    """Create kpi_definitions table."""
    logger.info("📋 Creating kpi_definitions table...")
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS kpi_definitions (
        id VARCHAR(100) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category VARCHAR(100) NOT NULL,
        formula TEXT NOT NULL,
        unit VARCHAR(50),
        format_type VARCHAR(20) DEFAULT 'number',
        frequency VARCHAR(100),
        importance TEXT,
        sector VARCHAR(50) DEFAULT 'cambio',
        is_active BOOLEAN DEFAULT true,
        chart_type VARCHAR(20) DEFAULT 'line',
        is_priority BOOLEAN DEFAULT false,
        display_order INTEGER DEFAULT 0,
        alert_config JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Create indexes for performance
    CREATE INDEX IF NOT EXISTS idx_kpi_category ON kpi_definitions(category);
    CREATE INDEX IF NOT EXISTS idx_kpi_sector ON kpi_definitions(sector);
    CREATE INDEX IF NOT EXISTS idx_kpi_active ON kpi_definitions(is_active);
    CREATE INDEX IF NOT EXISTS idx_kpi_priority ON kpi_definitions(is_priority);
    """
    
    cursor.execute(create_table_sql)
    logger.info("✅ kpi_definitions table created")

def create_kpi_alerts_table(cursor):
    """Create kpi_alerts table."""
    logger.info("📋 Creating kpi_alerts table...")
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS kpi_alerts (
        id SERIAL PRIMARY KEY,
        kpi_id VARCHAR(50) NOT NULL,
        client_id VARCHAR(50) NOT NULL,
        type VARCHAR(20) NOT NULL, -- 'above' ou 'below'
        threshold NUMERIC NOT NULL,
        message TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        UNIQUE(kpi_id, client_id)
    );
    
    CREATE INDEX IF NOT EXISTS idx_kpi_alerts_kpi_id ON kpi_alerts(kpi_id);
    CREATE INDEX IF NOT EXISTS idx_kpi_alerts_client_id ON kpi_alerts(client_id);
    """
    
    cursor.execute(create_table_sql)
    logger.info("✅ kpi_alerts table created")

def create_learning_tables(cursor):
    """Create learning and cache tables."""
    logger.info("📋 Creating learning tables...")
    
    create_tables_sql = """
    -- Query cache table
    CREATE TABLE IF NOT EXISTS query_cache (
        id VARCHAR(100) PRIMARY KEY,
        question TEXT NOT NULL,
        sql_query TEXT NOT NULL,
        question_embedding BYTEA,
        sql_embedding BYTEA,
        cached_results JSONB,
        business_analysis JSONB,
        suggestions JSONB,
        similarity_threshold FLOAT DEFAULT 0.85,
        client_id VARCHAR(50) DEFAULT 'L2M',
        sector VARCHAR(50) DEFAULT 'cambio',
        meta_data JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        access_count INTEGER DEFAULT 1,
        last_accessed TIMESTAMP DEFAULT NOW(),
        cache_version VARCHAR(10) DEFAULT '1.0'
    );
    
    -- Feedback corrections table
    CREATE TABLE IF NOT EXISTS feedback_corrections (
        id VARCHAR(100) PRIMARY KEY,
        question TEXT NOT NULL,
        original_query TEXT NOT NULL,
        corrected_query TEXT NOT NULL,
        feedback_type VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
    );
    
    -- Query history table
    CREATE TABLE IF NOT EXISTS query_history (
        id VARCHAR(255) PRIMARY KEY,
        question TEXT NOT NULL,
        sql_query TEXT,
        result_count INTEGER DEFAULT 0,
        success BOOLEAN DEFAULT false,
        error_message TEXT,
        has_feedback BOOLEAN DEFAULT false,
        feedback_type VARCHAR(50),
        feedback_explanation TEXT,
        corrected_query TEXT,
        user_id VARCHAR(255),
        session_id VARCHAR(255),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        meta_data JSONB DEFAULT '{}'
    );
    
    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_query_cache_client ON query_cache(client_id);
    CREATE INDEX IF NOT EXISTS idx_query_cache_sector ON query_cache(sector);
    CREATE INDEX IF NOT EXISTS idx_feedback_corrections_type ON feedback_corrections(feedback_type);
    CREATE INDEX IF NOT EXISTS idx_query_history_success ON query_history(success);
    """
    
    cursor.execute(create_tables_sql)
    logger.info("✅ Learning tables created")

def create_snapshots_table(cursor):
    """Create snapshots table for storing KPI snapshots."""
    logger.info("📋 Creating snapshots table...")
    
    # Enable UUID extension
    cursor.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";')
    
    cursor.execute('DROP TABLE IF EXISTS snapshots CASCADE;')
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS snapshots (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        client_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT NOW(),
        data JSONB NOT NULL,
        snapshot_metadata JSONB DEFAULT '{}'
    );
    
    CREATE INDEX IF NOT EXISTS idx_snapshots_client_id ON snapshots(client_id);
    CREATE INDEX IF NOT EXISTS idx_snapshots_created_at ON snapshots(created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_snapshots_client_created ON snapshots(client_id, created_at DESC);
    """
    cursor.execute(create_table_sql)
    logger.info("✅ snapshots table created")

def populate_kpi_definitions(cursor):
    """Populate kpi_definitions table with data from JSON."""
    logger.info("📊 Populating KPI definitions...")
    
    # Check if table already has data
    cursor.execute("SELECT COUNT(*) FROM kpi_definitions")
    count = cursor.fetchone()[0]
    
    if count > 0:
        logger.info(f"⚠️  Table already has {count} KPIs. Skipping population.")
        return
    
    # Critical KPIs to insert
    critical_kpis = [
        {
            'id': 'total_volume',
            'name': 'Volume Total Negociado',
            'description': 'Mede o tamanho da operação e é o indicador primário de crescimento',
            'category': 'operational',
            'formula': 'SUM(valor_me) FROM boleta WHERE data_operacao [periodo] AND id_cliente = [client_numeric_id]',
            'unit': 'Valor monetário (R$, US$, etc.)',
            'format_type': 'currency',
            'frequency': 'Diária/Semanal/Mensal',
            'importance': 'Indicador primário de crescimento',
            'is_priority': True,
            'chart_type': 'area',
            'display_order': 1
        },
        {
            'id': 'average_spread',
            'name': 'Spread Médio',
            'description': 'Margem de lucro por operação',
            'category': 'financial',
            'formula': 'AVG(spread) FROM boleta',
            'unit': 'Percentual (%)',
            'format_type': 'percentage',
            'frequency': 'Diária',
            'importance': 'Margem de lucro',
            'is_priority': True,
            'chart_type': 'line',
            'display_order': 2
        },
        {
            'id': 'conversion_rate',
            'name': 'Taxa de Conversão',
            'description': 'Eficácia comercial',
            'category': 'client',
            'formula': '(Número de leads convertidos em clientes / Total de leads) × 100',
            'unit': 'Percentual (%)',
            'format_type': 'percentage',
            'frequency': 'Mensal/Trimestral',
            'importance': 'Eficácia comercial',
            'is_priority': True,
            'chart_type': 'bar',
            'display_order': 3
        },
        {
            'id': 'average_ticket',
            'name': 'Ticket Médio',
            'description': 'Valor médio por operação',
            'category': 'operational',
            'formula': 'AVG(valor_me) FROM boleta',
            'unit': 'Valor monetário (R$, US$, etc.)',
            'format_type': 'currency',
            'frequency': 'Diária/Semanal',
            'importance': 'Valor médio por transação',
            'is_priority': True,
            'chart_type': 'line',
            'display_order': 4
        },
        {
            'id': 'retention_rate',
            'name': 'Taxa de Retenção',
            'description': 'Fidelização de clientes',
            'category': 'client',
            'formula': '(Número de clientes ativos no fim do período que também estavam ativos no início / Total de clientes ativos no início do período) × 100',
            'unit': 'Percentual (%)',
            'format_type': 'percentage',
            'frequency': 'Mensal/Trimestral/Anual',
            'importance': 'Fidelização de clientes',
            'is_priority': True,
            'chart_type': 'area',
            'display_order': 5
        },
        {
            'id': 'operations_per_analyst',
            'name': 'Operações por Analista',
            'description': 'Produtividade da equipe',
            'category': 'operational',
            'formula': 'COUNT(boleta) / COUNT(DISTINCT analista)',
            'unit': 'Número',
            'format_type': 'number',
            'frequency': 'Diária/Semanal',
            'importance': 'Produtividade da equipe',
            'is_priority': True,
            'chart_type': 'bar',
            'display_order': 6
        }
    ]
    
    # Insert KPIs
    insert_sql = """
    INSERT INTO kpi_definitions (
        id, name, description, category, formula, unit, format_type,
        frequency, importance, is_priority, chart_type, display_order
    ) VALUES (
        %(id)s, %(name)s, %(description)s, %(category)s, %(formula)s, %(unit)s, %(format_type)s,
        %(frequency)s, %(importance)s, %(is_priority)s, %(chart_type)s, %(display_order)s
    )
    """
    
    for kpi in critical_kpis:
        cursor.execute(insert_sql, kpi)
    
    logger.info(f"✅ Inserted {len(critical_kpis)} critical KPIs")

def main():
    """Main execution function."""
    logger.info("🚀 Starting Railway KPI tables setup...")
    
    # Get database connection
    conn = get_smart_db_connection()
    if not conn:
        logger.error("❌ Could not establish database connection")
        sys.exit(1)
    
    try:
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Create all tables
        create_kpi_definitions_table(cursor)
        create_kpi_alerts_table(cursor)
        create_learning_tables(cursor)
        create_snapshots_table(cursor)
        
        # Populate KPI definitions
        populate_kpi_definitions(cursor)
        
        logger.info("🎉 All tables created successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error during table creation: {e}")
        sys.exit(1)
    finally:
        conn.close()
        logger.info("🔒 Database connection closed")

if __name__ == "__main__":
    main()
