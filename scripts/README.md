# Scripts de Manutenção do DataHero4

Este diretório contém scripts utilitários para manutenção e administração do projeto DataHero4.

## 🧹 Script de Limpeza Automatizada

### `cleanup_maintenance.py`

Script Python para limpeza automatizada do codebase, removendo arquivos temporários, backups antigos e organizando a estrutura do projeto.

#### Uso

```bash
# Teste (modo dry-run)
python3 scripts/cleanup_maintenance.py --dry-run --verbose

# Execução real
python3 scripts/cleanup_maintenance.py --verbose

# Personalizar retenção
python3 scripts/cleanup_maintenance.py --backup-days 60 --log-days 14
```

#### Funcionalidades

- **Remove backups antigos**: Arquivos `.backup`, `*_backup_*`, backups SQL (padrão: 30+ dias)
- **Limpa arquivos temporários**: `*.tmp`, `*.temp`, `profiling_results_*`, `cache_test_results_*`
- **<PERSON><PERSON> logs antigos**: Logs `.log.*` e numerados (padrão: 7+ dias)
- **Organiza documentação**: Move arquivos de planejamento soltos para `docs/planning/`
- **Remove diretórios vazios**: Limpa estrutura de pastas desnecessárias

#### Parâmetros

- `--dry-run`: Simula operações sem executar (recomendado para teste)
- `--verbose, -v`: Mostra detalhes das operações
- `--backup-days N`: Dias para manter backups (padrão: 30)
- `--log-days N`: Dias para manter logs (padrão: 7)

#### Exemplo de Saída

```
🚀 Iniciando limpeza automatizada do DataHero4
🗑️  Removendo backups com mais de 30 dias...
🧹 Removendo arquivos temporários...
  ✅ Removido: apps/backend/temp/test_cache/cache.tmp
📋 Removendo logs com mais de 7 dias...
  ✅ Removido: apps/backend/logs/datahero_chat.log.5
📚 Organizando documentação...
📁 Removendo diretórios vazios...

📊 RELATÓRIO DE LIMPEZA
==================================================
Arquivos removidos: 15
Diretórios removidos: 3
✅ Limpeza concluída com sucesso!
```

## 🔄 Automação Recomendada

### Uso Manual Regular
```bash
# Limpeza semanal recomendada
python3 scripts/cleanup_maintenance.py --verbose
```

### Integração com Git Hooks (Opcional)
Adicionar ao `.git/hooks/post-merge`:
```bash
#!/bin/sh
python3 scripts/cleanup_maintenance.py --log-days 3 > /dev/null 2>&1
```

## ⚠️ Considerações de Segurança

1. **Sempre teste primeiro**: Use `--dry-run` antes de executar
2. **Backups importantes**: O script preserva backups recentes (configurável)
3. **Logs essenciais**: Mantém logs recentes para debug
4. **Reversibilidade**: Operações são permanentes - tenha backup do projeto

## 🔧 Outros Scripts

- `add-patterns-column.sql`: Adiciona coluna de padrões ao banco
- `check-tables.sql`: Verifica estrutura das tabelas
- `complete-schema-fix.sql`: Correções de schema do banco
- [Mais scripts SQL e Python para tarefas específicas...] 