#!/usr/bin/env python3
"""
Script simplificado para criar tabela snapshots no Railway PostgreSQL.
Conecta diretamente usando as credenciais do Railway.
"""

import psycopg2
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_snapshots_table_railway():
    """Criar tabela snapshots no Railway PostgreSQL usando credenciais diretas."""
    
    # Railway PostgreSQL credentials (from railway variables output)
    railway_connection = {
        'host': 'postgres.railway.internal', 
        'port': 5432,
        'database': 'railway',
        'user': 'postgres',
        'password': 'WePCXqlHNELnkbKxwqNaSaLQFAbykKSo'
    }
    
    try:
        logger.info("🚄 Connecting to Railway PostgreSQL...")
        conn = psycopg2.connect(**railway_connection)
        conn.autocommit = True
        cursor = conn.cursor()
        
        logger.info("📋 Creating snapshots table...")
        
        # Enable UUID extension
        cursor.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";')
        
        # Drop existing table if exists
        cursor.execute('DROP TABLE IF EXISTS snapshots CASCADE;')
        
        # Create snapshots table
        create_table_sql = """
        CREATE TABLE snapshots (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            client_id VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT NOW(),
            data JSONB NOT NULL,
            snapshot_metadata JSONB DEFAULT '{}'
        );
        
        CREATE INDEX idx_snapshots_client_id ON snapshots(client_id);
        CREATE INDEX idx_snapshots_created_at ON snapshots(created_at DESC);
        CREATE INDEX idx_snapshots_client_created ON snapshots(client_id, created_at DESC);
        """
        
        cursor.execute(create_table_sql)
        logger.info("✅ Snapshots table created successfully in Railway!")
        
        # Test table creation
        cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'snapshots';")
        count = cursor.fetchone()[0]
        logger.info(f"✅ Verification: Found {count} snapshots table(s)")
        
    except Exception as e:
        logger.error(f"❌ Error creating snapshots table: {e}")
        raise
    finally:
        if 'conn' in locals():
            conn.close()
            logger.info("🔒 Connection closed")

if __name__ == "__main__":
    create_snapshots_table_railway() 