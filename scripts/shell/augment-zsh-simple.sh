#!/usr/bin/env zsh
# Augment ZSH Simple - Configuração Global Simplificada
# Para usar: source augment-zsh-simple.sh

# Cores
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export NC='\033[0m'

# Função para log colorido
augment_log() {
    local level="$1"; shift
    case "$level" in
        "info") printf "${BLUE}ℹ️  $*${NC}\n" ;;
        "success") printf "${GREEN}✅  $*${NC}\n" ;;
        "warning") printf "${YELLOW}⚠️  $*${NC}\n" ;;
        "error") printf "${RED}❌  $*${NC}\n" ;;
        *) printf "${BLUE}💡  $*${NC}\n" ;;
    esac
}

# Detecta tipo de projeto
detect_project_type() {
    if [[ -f "package.json" && (-f "apps/backend/pyproject.toml" || -f "backend/pyproject.toml") ]]; then
        echo "monorepo"
    elif [[ -f "package.json" ]]; then
        echo "node"
    elif [[ -f "pyproject.toml" ]]; then
        echo "python"
    elif [[ -f "Cargo.toml" ]]; then
        echo "rust"
    elif [[ -f "go.mod" ]]; then
        echo "go"
    else
        echo "generic"
    fi
}

# Configura ambiente
setup_env() {
    local project_type=$(detect_project_type)
    case "$project_type" in
        "monorepo"|"node")
            export PYTHONUNBUFFERED=1
            export LOG_LEVEL=DEBUG
            [[ ":$PATH:" != *":./node_modules/.bin:"* ]] && export PATH="./node_modules/.bin:$PATH"
            ;;
        "python")
            export PYTHONUNBUFFERED=1
            export LOG_LEVEL=DEBUG
            ;;
    esac
}

# Comando universal para desenvolvimento
dev-project() {
    local project_type=$(detect_project_type)
    setup_env
    augment_log "info" "🚀 Iniciando desenvolvimento ($project_type)"
    
    case "$project_type" in
        "monorepo")
            if [[ -f "scripts/dev.sh" ]]; then
                exec zsh ./scripts/dev.sh "$@"
            elif [[ -f "turbo.json" ]]; then
                exec turbo dev "$@"
            else
                exec npm run dev "$@"
            fi
            ;;
        "node")
            exec npm run dev "$@"
            ;;
        "python")
            if [[ -f "pyproject.toml" ]]; then
                exec poetry run python -m src.main "$@"
            else
                exec python -m src.main "$@"
            fi
            ;;
        "rust")
            exec cargo run "$@"
            ;;
        "go")
            exec go run . "$@"
            ;;
        *)
            augment_log "error" "Tipo de projeto não suportado"
            ;;
    esac
}

# Comando universal para testes
test-project() {
    local project_type=$(detect_project_type)
    setup_env
    augment_log "info" "🧪 Executando testes ($project_type)"
    
    case "$project_type" in
        "monorepo")
            if [[ -f "scripts/test-all.sh" ]]; then
                exec zsh ./scripts/test-all.sh "$@"
            elif [[ -f "turbo.json" ]]; then
                exec turbo test "$@"
            else
                exec npm test "$@"
            fi
            ;;
        "node")
            exec npm test "$@"
            ;;
        "python")
            if [[ -f "pyproject.toml" ]]; then
                exec poetry run pytest "$@"
            else
                exec pytest "$@"
            fi
            ;;
        "rust")
            exec cargo test "$@"
            ;;
        "go")
            exec go test ./... "$@"
            ;;
        *)
            augment_log "error" "Tipo de projeto não suportado"
            ;;
    esac
}

# Status do projeto
project-status() {
    local project_type=$(detect_project_type)
    augment_log "info" "📊 Status do Projeto"
    echo -e "${GREEN}Tipo:${NC} $project_type"
    echo -e "${GREEN}Diretório:${NC} $(pwd)"
    echo -e "${GREEN}Shell:${NC} $SHELL"
    
    if command -v git &> /dev/null && [[ -d ".git" ]]; then
        echo -e "${GREEN}Git Branch:${NC} $(git branch --show-current 2>/dev/null || echo 'N/A')"
    fi
    
    case "$project_type" in
        "monorepo"|"node")
            echo -e "${GREEN}Node.js:${NC} $(node --version 2>/dev/null || echo 'N/A')"
            ;;
        "python")
            echo -e "${GREEN}Python:${NC} $(python --version 2>/dev/null || echo 'N/A')"
            [[ -f "pyproject.toml" ]] && echo -e "${GREEN}Poetry:${NC} $(poetry --version 2>/dev/null || echo 'N/A')"
            ;;
        "rust")
            echo -e "${GREEN}Rust:${NC} $(rustc --version 2>/dev/null || echo 'N/A')"
            ;;
        "go")
            echo -e "${GREEN}Go:${NC} $(go version 2>/dev/null || echo 'N/A')"
            ;;
    esac
}

# Função de ajuda
augment-help() {
    augment_log "info" "🚀 Augment ZSH - Comandos Universais"
    echo -e "${YELLOW}Comandos:${NC}"
    echo "  dev-project    - Inicia desenvolvimento (auto-detecta)"
    echo "  test-project   - Executa testes (auto-detecta)"
    echo "  project-status - Status do projeto"
    echo "  augment-help   - Esta ajuda"
    echo -e "${YELLOW}Git Shortcuts:${NC}"
    echo "  gs/ga/gc/gp/gl - Shortcuts para git"
    echo -e "${BLUE}💡 Comandos se adaptam ao tipo de projeto!${NC}"
}

# Aliases globais
alias st="project-status"
alias gs="git status"
alias ga="git add"
alias gc="git commit"
alias gp="git push"
alias gl="git log --oneline -10"

# Auto-setup quando carregar
setup_env

augment_log "success" "Augment ZSH Simple carregado! Digite 'augment-help' para ver comandos."
