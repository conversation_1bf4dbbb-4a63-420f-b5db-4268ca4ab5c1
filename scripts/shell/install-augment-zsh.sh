#!/usr/bin/env zsh
# Instalador Global do Augment ZSH
# Execute: curl -sSL https://raw.githubusercontent.com/seu-repo/install-augment-zsh.sh | zsh

set -euo pipefail

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { printf "${BLUE}ℹ️  $1${NC}\n"; }
log_success() { printf "${GREEN}✅  $1${NC}\n"; }
log_warning() { printf "${YELLOW}⚠️  $1${NC}\n"; }
log_error() { printf "${RED}❌  $1${NC}\n"; }

echo ""
log_info "🚀 Instalando Augment ZSH Global Configuration..."
echo ""

# Verifica se zsh está disponível
if ! command -v zsh &> /dev/null; then
    log_error "zsh não encontrado. Instale o zsh primeiro."
    exit 1
fi

# Configura zsh como shell padrão se necessário
if [[ "$SHELL" != *"zsh"* ]]; then
    log_warning "Shell atual: $SHELL"
    log_info "Configurando zsh como shell padrão..."
    
    ZSH_PATH=$(which zsh)
    if chsh -s "$ZSH_PATH" 2>/dev/null; then
        log_success "zsh configurado como shell padrão"
    else
        log_warning "Não foi possível mudar automaticamente. Execute: chsh -s $ZSH_PATH"
    fi
fi

# Cria backup do .zshrc existente
if [[ -f "$HOME/.zshrc" ]]; then
    log_info "Fazendo backup do .zshrc existente..."
    cp "$HOME/.zshrc" "$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"
    log_success "Backup criado"
fi

# Cria o arquivo de configuração global
log_info "Criando configuração global do Augment ZSH..."

cat > "$HOME/.augment-zsh-config" << 'EOF'
# Augment ZSH Global Configuration
# Configuração universal para desenvolvimento com Augment

# Cores para output
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export PURPLE='\033[0;35m'
export CYAN='\033[0;36m'
export NC='\033[0m'

# Função para log colorido
augment_log() {
    local level="$1"; shift
    case "$level" in
        "info") printf "${BLUE}ℹ️  $*${NC}\n" ;;
        "success") printf "${GREEN}✅  $*${NC}\n" ;;
        "warning") printf "${YELLOW}⚠️  $*${NC}\n" ;;
        "error") printf "${RED}❌  $*${NC}\n" ;;
        "debug") printf "${PURPLE}🔍  $*${NC}\n" ;;
        *) printf "${CYAN}💡  $*${NC}\n" ;;
    esac
}

# Detecta tipo de projeto
detect_project_type() {
    if [[ -f "package.json" ]]; then
        if [[ -f "apps/backend/pyproject.toml" || -f "backend/pyproject.toml" ]]; then
            echo "monorepo"
        else
            echo "node"
        fi
    elif [[ -f "pyproject.toml" ]]; then echo "python"
    elif [[ -f "Cargo.toml" ]]; then echo "rust"
    elif [[ -f "go.mod" ]]; then echo "go"
    elif [[ -f "pom.xml" || -f "build.gradle" ]]; then echo "java"
    elif [[ -f "composer.json" ]]; then echo "php"
    else echo "generic"
    fi
}

# Configura ambiente baseado no projeto
setup_project_env() {
    local project_type=$(detect_project_type)
    case "$project_type" in
        "monorepo"|"node")
            export PYTHONUNBUFFERED=1
            export LOG_LEVEL=DEBUG
            [[ ":$PATH:" != *":./node_modules/.bin:"* ]] && export PATH="./node_modules/.bin:$PATH"
            ;;
        "python")
            export PYTHONUNBUFFERED=1
            export LOG_LEVEL=DEBUG
            ;;
    esac
}

# Comando universal para desenvolvimento
dev() {
    local project_type=$(detect_project_type)
    setup_project_env
    augment_log "info" "🚀 Iniciando desenvolvimento ($project_type)"
    
    case "$project_type" in
        "monorepo")
            if [[ -f "scripts/dev.sh" ]]; then exec zsh ./scripts/dev.sh "$@"
            elif [[ -f "turbo.json" ]]; then exec turbo dev "$@"
            else exec npm run dev "$@"; fi ;;
        "node") exec npm run dev "$@" ;;
        "python")
            if [[ -f "pyproject.toml" ]]; then exec poetry run python -m src.main "$@"
            else exec python -m src.main "$@"; fi ;;
        "rust") exec cargo run "$@" ;;
        "go") exec go run . "$@" ;;
        *) augment_log "error" "Tipo de projeto não suportado" ;;
    esac
}

# Comando universal para testes
test-all() {
    local project_type=$(detect_project_type)
    setup_project_env
    augment_log "info" "🧪 Executando testes ($project_type)"
    
    case "$project_type" in
        "monorepo")
            if [[ -f "scripts/test-all.sh" ]]; then exec zsh ./scripts/test-all.sh "$@"
            elif [[ -f "turbo.json" ]]; then exec turbo test "$@"
            else exec npm test "$@"; fi ;;
        "node") exec npm test "$@" ;;
        "python")
            if [[ -f "pyproject.toml" ]]; then exec poetry run pytest "$@"
            else exec pytest "$@"; fi ;;
        "rust") exec cargo test "$@" ;;
        "go") exec go test ./... "$@" ;;
        *) augment_log "error" "Tipo de projeto não suportado" ;;
    esac
}

# Status do projeto
project-status() {
    local project_type=$(detect_project_type)
    augment_log "info" "📊 Status do Projeto"
    echo -e "${GREEN}Tipo:${NC} $project_type"
    echo -e "${GREEN}Diretório:${NC} $(pwd)"
    echo -e "${GREEN}Shell:${NC} $SHELL"
    
    if command -v git &> /dev/null && [[ -d ".git" ]]; then
        echo -e "${GREEN}Git Branch:${NC} $(git branch --show-current 2>/dev/null || echo 'N/A')"
    fi
    
    case "$project_type" in
        "monorepo"|"node")
            echo -e "${GREEN}Node.js:${NC} $(node --version 2>/dev/null || echo 'N/A')" ;;
        "python")
            echo -e "${GREEN}Python:${NC} $(python --version 2>/dev/null || echo 'N/A')"
            [[ -f "pyproject.toml" ]] && echo -e "${GREEN}Poetry:${NC} $(poetry --version 2>/dev/null || echo 'N/A')" ;;
        "rust")
            echo -e "${GREEN}Rust:${NC} $(rustc --version 2>/dev/null || echo 'N/A')" ;;
        "go")
            echo -e "${GREEN}Go:${NC} $(go version 2>/dev/null || echo 'N/A')" ;;
    esac
}

# Auto-setup quando entrar em diretório
auto-setup() {
    if [[ -f "package.json" || -f "pyproject.toml" || -f "Cargo.toml" || -f "go.mod" ]]; then
        setup_project_env
    fi
}

# Hook para mudança de diretório
chpwd() { auto-setup; }

# Aliases globais
alias st="project-status"
alias gs="git status"
alias ga="git add"
alias gc="git commit"
alias gp="git push"
alias gl="git log --oneline -10"

# Função de ajuda
augment-help() {
    augment_log "info" "🚀 Augment ZSH - Comandos Universais"
    echo -e "${YELLOW}Desenvolvimento:${NC}"
    echo "  dev    - Inicia desenvolvimento (auto-detecta projeto)"
    echo "  test   - Executa testes (auto-detecta projeto)"
    echo "  st     - Status do projeto"
    echo -e "${YELLOW}Git:${NC}"
    echo "  gs/ga/gc/gp/gl - Shortcuts para git"
    echo -e "${CYAN}💡 Comandos se adaptam ao tipo de projeto!${NC}"
}

# Executa auto-setup
auto-setup
EOF

# Adiciona source ao .zshrc
log_info "Configurando .zshrc..."

# Remove linhas antigas se existirem
if [[ -f "$HOME/.zshrc" ]]; then
    grep -v "source.*\.augment-zsh-config" "$HOME/.zshrc" > "$HOME/.zshrc.tmp" || true
    mv "$HOME/.zshrc.tmp" "$HOME/.zshrc"
fi

# Adiciona nova configuração
cat >> "$HOME/.zshrc" << 'EOF'

# Augment ZSH Global Configuration
if [[ -f "$HOME/.augment-zsh-config" ]]; then
    source "$HOME/.augment-zsh-config"
fi
EOF

log_success "Configuração global criada em ~/.augment-zsh-config"
log_success "Configuração adicionada ao ~/.zshrc"

echo ""
log_info "🎯 Instalação concluída!"
echo ""
log_warning "Para ativar as configurações:"
echo "  1. Reinicie o terminal OU"
echo "  2. Execute: source ~/.zshrc"
echo ""
log_info "Comandos disponíveis em qualquer projeto:"
echo "  • dev        - Desenvolvimento automático"
echo "  • test       - Testes automáticos"
echo "  • st         - Status do projeto"
echo "  • augment-help - Ajuda completa"
echo ""
log_success "Agora você pode usar o Augment ZSH em qualquer projeto! 🚀"
