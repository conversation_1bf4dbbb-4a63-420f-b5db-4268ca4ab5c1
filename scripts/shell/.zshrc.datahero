# DataHero4 Project ZSH Configuration
# Source this file to configure your zsh for DataHero4 development
# Usage: source .zshrc.datahero

# Cores para output
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export NC='\033[0m' # No Color

# Configurações específicas do DataHero4
export PYTHONUNBUFFERED=1
export LOG_LEVEL=DEBUG
export UVICORN_LOG_LEVEL=debug

# Adiciona node_modules/.bin ao PATH
if [[ ":$PATH:" != *":./node_modules/.bin:"* ]]; then
    export PATH="./node_modules/.bin:$PATH"
fi

# Aliases úteis para o projeto
alias dh-dev='./scripts/zsh-wrapper.sh dev'
alias dh-test='./scripts/zsh-wrapper.sh test'
alias dh-validate='./scripts/zsh-wrapper.sh validate'
alias dh-setup='./scripts/zsh-wrapper.sh setup'
alias dh-turbo='./scripts/zsh-wrapper.sh turbo'
alias dh-npm='./scripts/zsh-wrapper.sh npm'
alias dh-poetry='./scripts/zsh-wrapper.sh poetry'
alias dh-git='./scripts/zsh-wrapper.sh git'

# Aliases diretos para scripts (removido dev para evitar conflito)
alias test-all='./scripts/test-all.sh'
alias validate-deps='./scripts/validate-deps.sh'

# Funções úteis
dh-status() {
    echo -e "${BLUE}📊 Status do DataHero4${NC}"
    echo -e "${GREEN}Shell:${NC} $SHELL"
    echo -e "${GREEN}Diretório:${NC} $(pwd)"
    echo -e "${GREEN}Git Branch:${NC} $(git branch --show-current 2>/dev/null || echo 'N/A')"
    echo -e "${GREEN}Node.js:${NC} $(node --version 2>/dev/null || echo 'N/A')"
    echo -e "${GREEN}Poetry:${NC} $(poetry --version 2>/dev/null || echo 'N/A')"
    echo -e "${GREEN}Turbo:${NC} $(turbo --version 2>/dev/null || echo 'N/A')"
}

dh-help() {
    echo -e "${BLUE}🚀 DataHero4 - Comandos Disponíveis${NC}"
    echo ""
    echo -e "${YELLOW}Desenvolvimento:${NC}"
    echo "  dh-dev       - Inicia ambiente de desenvolvimento"
    echo "  dh-test      - Executa todos os testes"
    echo "  dh-validate  - Valida dependências"
    echo "  dh-setup     - Configura terminal zsh"
    echo ""
    echo -e "${YELLOW}Ferramentas:${NC}"
    echo "  dh-turbo     - Executa comandos turbo"
    echo "  dh-npm       - Executa comandos npm"
    echo "  dh-poetry    - Executa comandos poetry"
    echo "  dh-git       - Executa comandos git"
    echo ""
    echo -e "${YELLOW}Scripts Diretos:${NC}"
    echo "  dev          - ./scripts/dev.sh"
    echo "  test-all     - ./scripts/test-all.sh"
    echo "  validate-deps - ./scripts/validate-deps.sh"
    echo ""
    echo -e "${YELLOW}Utilitários:${NC}"
    echo "  dh-status    - Mostra status do projeto"
    echo "  dh-help      - Mostra esta ajuda"
}

# Verifica se estamos no diretório correto
if [[ -f "package.json" && -d "apps" ]]; then
    echo -e "${GREEN}✅ DataHero4 zsh configurado!${NC}"
    echo -e "${BLUE}💡 Digite 'dh-help' para ver comandos disponíveis${NC}"
else
    echo -e "${YELLOW}⚠️  Não parece ser o diretório do DataHero4${NC}"
    echo -e "${BLUE}💡 Navegue para o diretório do projeto primeiro${NC}"
fi
