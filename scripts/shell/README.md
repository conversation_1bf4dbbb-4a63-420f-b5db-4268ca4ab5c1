# Shell Scripts

Esta pasta contém scripts de configuração e automação para o shell.

## Arquivos

### `install-augment-zsh.sh`
Script de instalação global do Augment ZSH com configurações universais para desenvolvimento.

**Funcionalidades:**
- Configura zsh como shell padrão
- Cria configuração global para desenvolvimento
- Detecta automaticamente tipo de projeto
- Comandos universais (`dev`, `test-all`, `project-status`)

**Uso:**
```bash
curl -sSL https://raw.githubusercontent.com/seu-repo/install-augment-zsh.sh | zsh
```

### `augment-zsh-simple.sh`
Versão simplificada do script de configuração ZSH.

### `.zshrc.datahero`
Configuração específica do ZSH para o projeto DataHero4.

**Funcionalidades:**
- Aliases específicos do projeto
- Configurações de ambiente
- Shortcuts para comandos comuns

**Uso:**
```bash
# Adicionar ao seu .zshrc:
source /path/to/datahero4/scripts/shell/.zshrc.datahero
```

## Instalação

Para usar as configurações do shell:

1. Execute o script de instalação global, ou
2. Source o arquivo `.zshrc.datahero` no seu `.zshrc`

## Nota

Estes scripts foram movidos do root para manter a organização do repositório seguindo as melhores práticas. 