#!/usr/bin/env python3
"""
Análise detalhada da taxa de satisfação baixa (22.2%).
Investiga feedbacks negativos vs positivos para identificar problemas de qualidade.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'apps', 'backend'))

import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from src.utils.learning_db_utils import get_db_manager
from sqlalchemy import text

def analyze_satisfaction_rate():
    """
    Analisa a taxa de satisfação baixa investigando feedbacks negativos vs positivos.
    """
    
    print("📊 ANÁLISE DA TAXA DE SATISFAÇÃO BAIXA (22.2%)")
    print("=" * 60)
    print(f"⏰ Análise iniciada em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    db_manager = get_db_manager()
    if not db_manager:
        print("❌ DB manager não disponível")
        return
    
    try:
        with db_manager.get_session() as session:
            
            # 1. ANÁLISE GERAL DE FEEDBACKS
            print("📈 1. ANÁLISE GERAL DE FEEDBACKS")
            print("-" * 40)
            
            # Contar feedbacks por tipo
            feedback_stats = session.execute(text("""
                SELECT 
                    CASE 
                        WHEN meta_data->>'feedback_type' = 'positive' THEN 'Positivo'
                        WHEN meta_data->>'feedback_type' = 'negative' THEN 'Negativo'
                        ELSE 'Não classificado'
                    END as tipo_feedback,
                    COUNT(*) as quantidade
                FROM feedback_corrections 
                WHERE created_at >= NOW() - INTERVAL '30 days'
                GROUP BY meta_data->>'feedback_type'
                ORDER BY quantidade DESC
            """)).fetchall()
            
            total_feedbacks = sum(row[1] for row in feedback_stats)
            
            print(f"📊 Feedbacks dos últimos 30 dias:")
            for tipo, quantidade in feedback_stats:
                percentage = (quantidade / total_feedbacks * 100) if total_feedbacks > 0 else 0
                print(f"   {tipo}: {quantidade} ({percentage:.1f}%)")
            
            print(f"   Total: {total_feedbacks} feedbacks")
            
            if total_feedbacks > 0:
                negativos = next((q for t, q in feedback_stats if t == 'Negativo'), 0)
                positivos = next((q for t, q in feedback_stats if t == 'Positivo'), 0)
                taxa_satisfacao = (positivos / (positivos + negativos) * 100) if (positivos + negativos) > 0 else 0
                print(f"   Taxa de satisfação atual: {taxa_satisfacao:.1f}%")
            
            # 2. ANÁLISE DETALHADA DOS FEEDBACKS NEGATIVOS
            print(f"\n🚨 2. ANÁLISE DOS FEEDBACKS NEGATIVOS")
            print("-" * 40)
            
            negative_feedbacks = session.execute(text("""
                SELECT 
                    id, question, user_comment, category, explanation,
                    created_at, meta_data
                FROM feedback_corrections 
                WHERE meta_data->>'feedback_type' = 'negative'
                AND created_at >= NOW() - INTERVAL '30 days'
                ORDER BY created_at DESC
            """)).fetchall()
            
            print(f"📋 Total de feedbacks negativos: {len(negative_feedbacks)}")
            
            # Categorizar problemas
            problem_categories = defaultdict(list)
            
            for feedback in negative_feedbacks:
                category = feedback[3] or "não_categorizado"
                comment = feedback[2] or ""
                explanation = feedback[4] or ""
                
                problem_categories[category].append({
                    "id": feedback[0],
                    "question": feedback[1],
                    "comment": comment,
                    "explanation": explanation,
                    "created_at": feedback[5]
                })
            
            print(f"\n📂 Problemas por categoria:")
            for category, issues in problem_categories.items():
                print(f"   {category}: {len(issues)} casos")
                
                # Mostrar exemplos
                for i, issue in enumerate(issues[:2], 1):  # Máximo 2 exemplos por categoria
                    print(f"     {i}. {issue['question'][:50]}...")
                    print(f"        Problema: {issue['comment'][:80]}...")
                    print()
            
            # 3. ANÁLISE DOS FEEDBACKS POSITIVOS
            print(f"✅ 3. ANÁLISE DOS FEEDBACKS POSITIVOS")
            print("-" * 40)
            
            positive_feedbacks = session.execute(text("""
                SELECT 
                    id, question, user_comment, explanation,
                    created_at, meta_data
                FROM feedback_corrections 
                WHERE meta_data->>'feedback_type' = 'positive'
                AND created_at >= NOW() - INTERVAL '30 days'
                ORDER BY created_at DESC
            """)).fetchall()
            
            print(f"📋 Total de feedbacks positivos: {len(positive_feedbacks)}")
            
            if positive_feedbacks:
                print(f"\n💡 Exemplos de sucesso:")
                for i, feedback in enumerate(positive_feedbacks[:3], 1):
                    print(f"   {i}. {feedback[1][:60]}...")
                    if feedback[2]:
                        print(f"      Comentário: {feedback[2][:80]}...")
                    print()
            
            # 4. ANÁLISE TEMPORAL
            print(f"📅 4. ANÁLISE TEMPORAL")
            print("-" * 40)
            
            temporal_analysis = session.execute(text("""
                SELECT 
                    DATE(created_at) as data,
                    meta_data->>'feedback_type' as tipo,
                    COUNT(*) as quantidade
                FROM feedback_corrections 
                WHERE created_at >= NOW() - INTERVAL '14 days'
                GROUP BY DATE(created_at), meta_data->>'feedback_type'
                ORDER BY data DESC, tipo
            """)).fetchall()
            
            # Agrupar por data
            daily_stats = defaultdict(lambda: {"positive": 0, "negative": 0})
            
            for data, tipo, quantidade in temporal_analysis:
                if tipo in ["positive", "negative"]:
                    daily_stats[data][tipo] = quantidade
            
            print(f"📈 Tendência dos últimos 14 dias:")
            for data in sorted(daily_stats.keys(), reverse=True)[:7]:  # Últimos 7 dias
                pos = daily_stats[data]["positive"]
                neg = daily_stats[data]["negative"]
                total = pos + neg
                taxa = (pos / total * 100) if total > 0 else 0
                print(f"   {data}: {pos}+ / {neg}- (taxa: {taxa:.1f}%)")
            
            # 5. ANÁLISE DE PADRÕES DE PROBLEMAS
            print(f"\n🔍 5. PADRÕES DE PROBLEMAS IDENTIFICADOS")
            print("-" * 40)
            
            # Palavras-chave mais comuns nos feedbacks negativos
            all_comments = []
            for feedback in negative_feedbacks:
                comment = (feedback[2] or "").lower()
                explanation = (feedback[4] or "").lower()
                all_comments.extend(comment.split())
                all_comments.extend(explanation.split())
            
            # Filtrar palavras relevantes
            relevant_words = [word for word in all_comments 
                            if len(word) > 3 and word not in ['para', 'com', 'que', 'não', 'uma', 'dos', 'das', 'por', 'como']]
            
            word_freq = Counter(relevant_words)
            
            print(f"🔤 Palavras mais frequentes nos problemas:")
            for word, freq in word_freq.most_common(10):
                print(f"   '{word}': {freq} ocorrências")
            
            # 6. RECOMENDAÇÕES PARA MELHORIA
            print(f"\n💡 6. RECOMENDAÇÕES PARA MELHORIA")
            print("-" * 40)
            
            recommendations = []
            
            # Baseado nas categorias de problemas
            if "sql_incorreto" in problem_categories:
                recommendations.append("Melhorar validação e geração de SQL")
            
            if "dados_incorretos" in problem_categories:
                recommendations.append("Revisar lógica de filtragem de dados")
            
            if "sem_dados" in problem_categories:
                recommendations.append("Melhorar tratamento de casos sem resultados")
            
            # Baseado nas palavras-chave
            common_issues = [word for word, freq in word_freq.most_common(5)]
            if "dados" in common_issues:
                recommendations.append("Focar na qualidade dos dados retornados")
            
            if "ano" in common_issues or "data" in common_issues:
                recommendations.append("Melhorar tratamento de filtros temporais")
            
            if not recommendations:
                recommendations.append("Continuar monitoramento para identificar padrões")
            
            print(f"📋 Recomendações prioritárias:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
            
            # 7. MÉTRICAS DE QUALIDADE
            print(f"\n📊 7. MÉTRICAS DE QUALIDADE ATUAL")
            print("-" * 40)
            
            # Calcular métricas
            if total_feedbacks > 0:
                negativos = len(negative_feedbacks)
                positivos = len(positive_feedbacks)
                
                print(f"📈 Métricas dos últimos 30 dias:")
                print(f"   Feedbacks positivos: {positivos}")
                print(f"   Feedbacks negativos: {negativos}")
                print(f"   Taxa de satisfação: {taxa_satisfacao:.1f}%")
                print(f"   Meta recomendada: ≥ 80%")
                print(f"   Melhoria necessária: +{80 - taxa_satisfacao:.1f} pontos percentuais")
                
                # Projeção de melhoria
                if negativos > 0:
                    problemas_resolvidos_necessarios = int((negativos * 0.7))  # Resolver 70% dos problemas
                    nova_taxa = ((positivos + problemas_resolvidos_necessarios) / 
                               (positivos + negativos - problemas_resolvidos_necessarios) * 100)
                    print(f"   Projeção (resolvendo 70% dos problemas): {nova_taxa:.1f}%")
            
            return {
                "total_feedbacks": total_feedbacks,
                "feedbacks_negativos": len(negative_feedbacks),
                "feedbacks_positivos": len(positive_feedbacks),
                "taxa_satisfacao": taxa_satisfacao,
                "categorias_problemas": dict(problem_categories),
                "recomendacoes": recommendations,
                "palavras_chave_problemas": dict(word_freq.most_common(10))
            }
            
    except Exception as e:
        print(f"❌ Erro na análise: {e}")
        return None

def generate_improvement_plan(analysis_result):
    """
    Gera plano de melhoria baseado na análise.
    """
    
    if not analysis_result:
        return
    
    print(f"\n🎯 PLANO DE MELHORIA DA SATISFAÇÃO")
    print("=" * 50)
    
    taxa_atual = analysis_result["taxa_satisfacao"]
    meta = 80.0
    
    print(f"📊 Situação atual: {taxa_atual:.1f}% de satisfação")
    print(f"🎯 Meta: {meta:.1f}% de satisfação")
    print(f"📈 Melhoria necessária: +{meta - taxa_atual:.1f} pontos percentuais")
    print()
    
    # Ações específicas baseadas nos problemas identificados
    print(f"🔧 AÇÕES ESPECÍFICAS:")
    
    categorias = analysis_result["categorias_problemas"]
    
    if "sql_incorreto" in categorias:
        print(f"   1. SQL Incorreto ({len(categorias['sql_incorreto'])} casos):")
        print(f"      - Melhorar prompts de geração de SQL")
        print(f"      - Implementar mais validações pré-execução")
        print(f"      - Treinar modelo com exemplos corretos")
    
    if "dados_incorretos" in categorias:
        print(f"   2. Dados Incorretos ({len(categorias['dados_incorretos'])} casos):")
        print(f"      - Revisar lógica de filtragem")
        print(f"      - Validar integridade dos dados")
        print(f"      - Melhorar interpretação de datas")
    
    if "sem_dados" in categorias:
        print(f"   3. Sem Dados ({len(categorias['sem_dados'])} casos):")
        print(f"      - Melhorar mensagens explicativas")
        print(f"      - Sugerir alternativas quando não há dados")
        print(f"      - Verificar se filtros estão muito restritivos")
    
    # Cronograma
    print(f"\n📅 CRONOGRAMA SUGERIDO:")
    print(f"   Semana 1-2: Implementar validações críticas")
    print(f"   Semana 3-4: Melhorar geração de SQL")
    print(f"   Semana 5-6: Otimizar tratamento de dados")
    print(f"   Semana 7-8: Testes e ajustes finais")
    
    # Métricas de acompanhamento
    print(f"\n📊 MÉTRICAS DE ACOMPANHAMENTO:")
    print(f"   - Taxa de satisfação semanal")
    print(f"   - Número de feedbacks negativos por categoria")
    print(f"   - Tempo médio de resolução de problemas")
    print(f"   - Taxa de reincidência de problemas")

if __name__ == "__main__":
    print("🚀 Iniciando análise de satisfação...")
    
    result = analyze_satisfaction_rate()
    
    if result:
        generate_improvement_plan(result)
        print(f"\n✅ Análise concluída com sucesso!")
    else:
        print(f"\n❌ Falha na análise.")
    
    print(f"\n🏁 Análise finalizada em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
