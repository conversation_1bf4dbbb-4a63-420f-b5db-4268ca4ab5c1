# 🚀 Plano de Otimização de Performance do Dashboard

## 📋 Sumário Executivo

O dashboard está levando aproximadamente **20 segundos** para carregar, quando o esperado seria **menos de 2 segundos**. <PERSON><PERSON><PERSON> disso, os filtros temporais estão atualizando apenas os gráficos, mas não os valores consolidados dos KPIs.

## 🔍 Problemas Identificados

### 1. Performance (20s de carregamento)

#### 1.1 Queries SQL Ineficientes
- **Problema**: Cada KPI executa uma query SQL separada sequencialmente
- **Impacto**: 6 KPIs x ~3s cada = 18s+ de tempo total
- **Evidência**: C<PERSON>digo em `kpi_service.py` mostra queries individuais sem paralelização

#### 1.2 Cache Mal Configurado
- **Problema**: TTL de apenas 5 minutos e invalidação muito agressiva
- **Impacto**: Cache raramente é aproveitado, gerando recálculos constantes
- **Evidência**: `self._cache_ttl = 300` em `kpi_service.py`

#### 1.3 Dados de Gráfico Calculados em Tempo Real
- **Problema**: Para cada KPI, são geradas queries adicionais para dados históricos
- **Impacto**: Duplica o número de queries necessárias
- **Evidência**: Método `_generate_real_chart_data` executa queries separadas

### 2. Bug dos Filtros Temporais

#### 2.1 Valores Não Atualizam com Filtros
- **Problema**: `currentValue` e `changePercent` não são recalculados quando filtros mudam
- **Impacto**: Usuário vê valores inconsistentes (total de sempre vs gráfico filtrado)
- **Evidência**: Frontend recebe valores cacheados sem considerar filtros

## 📊 Métricas de Performance Atuais

```
Dashboard Load Time: ~20,000ms
├── API Call: ~19,500ms
│   ├── KPI Calculations: ~18,000ms
│   │   ├── SQL Queries: ~15,000ms
│   │   └── Data Processing: ~3,000ms
│   └── Network Transfer: ~1,500ms
└── Frontend Rendering: ~500ms
```

## 🎯 Objetivos de Performance

- **Tempo de carregamento inicial**: < 2 segundos
- **Mudança de filtros**: < 500ms
- **Taxa de cache hit**: > 80%
- **Consistência de dados**: 100% (valores e gráficos sincronizados)

## 🛠️ Soluções Propostas

### Fase 1: Otimizações Imediatas (1-2 dias)

#### 1.1 Implementar Paralelização de Queries
```python
# apps/backend/src/services/kpi_service_optimized.py
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def calculate_kpis_parallel(self, kpi_ids: List[str], filters: Dict):
    """Calcula múltiplos KPIs em paralelo."""
    with ThreadPoolExecutor(max_workers=6) as executor:
        tasks = []
        for kpi_id in kpi_ids:
            task = executor.submit(
                self._calculate_single_kpi, 
                kpi_id, 
                filters
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return results
```

#### 1.2 Aumentar TTL do Cache
```python
# Configuração inteligente de TTL baseada no timeframe
def _get_intelligent_ttl(self, timeframe: str) -> int:
    ttl_map = {
        '1d': 300,      # 5 minutos para dados diários
        'week': 1800,   # 30 minutos para dados semanais
        'month': 3600,  # 1 hora para dados mensais
        'quarter': 7200 # 2 horas para dados trimestrais
    }
    return ttl_map.get(timeframe, 1800)
```

#### 1.3 Usar Snapshot Pré-Calculado
```python
# Modificar useKpiData para usar snapshot primeiro
const { kpis, isLoading } = useKpiData(filters) => {
    // 1. Tentar snapshot primeiro (instantâneo)
    const snapshot = await getDashboardSnapshot();
    if (snapshot.success) {
        setKpis(convertSnapshotToKpiData(snapshot));
        setIsLoading(false);
        
        // 2. Atualizar com dados filtrados em background
        getDashboardKpis(filters).then(updateKpis);
    }
}
```

### Fase 2: Correção do Bug de Filtros (1 dia)

#### 2.1 Garantir Recálculo com Filtros
```python
# Backend: Incluir filtros no cálculo de valores
def calculate_kpi_value(self, kpi_id: str, timeframe: str, currency: str):
    # Aplicar filtros SQL
    timeframe_sql = self._get_timeframe_sql(timeframe)
    currency_sql = self._get_currency_sql(currency)
    
    # Calcular valor E percentual de mudança com filtros
    current_value = self._execute_filtered_query(kpi_id, timeframe_sql, currency_sql)
    previous_value = self._execute_filtered_query(kpi_id, prev_timeframe_sql, currency_sql)
    
    change_percent = ((current_value - previous_value) / previous_value) * 100
    
    return {
        'currentValue': current_value,
        'changePercent': change_percent,
        'trend': 'up' if change_percent > 0 else 'down'
    }
```

#### 2.2 Frontend: Sincronizar Valores e Gráficos
```typescript
// Garantir que valores e gráficos usem os mesmos dados filtrados
const { kpis } = useKpiData(filters);

// Cada KPI deve ter valores calculados com os mesmos filtros dos gráficos
kpis.forEach(kpi => {
    console.assert(
        kpi.metadata.timeframe === filters.timeframe,
        'KPI values must match filter timeframe'
    );
});
```

### Fase 3: Otimizações Avançadas (3-5 dias)

#### 3.1 Implementar Cache Warming
```python
# apps/backend/src/services/cache_warming_optimized.py
class OptimizedCacheWarmingService:
    async def warm_critical_kpis(self):
        """Pré-calcula KPIs críticos em horários de baixo uso."""
        critical_kpis = ['total_volume', 'average_ticket', 'average_spread']
        timeframes = ['1d', 'week', 'month']
        currencies = ['all', 'usd', 'eur']
        
        # Agendar para rodar às 3AM
        for kpi in critical_kpis:
            for tf in timeframes:
                for curr in currencies:
                    await self.calculate_and_cache(kpi, tf, curr)
```

#### 3.2 Criar Índices Otimizados
```sql
-- Índices compostos para queries mais rápidas
CREATE INDEX idx_boleta_filters ON boleta(data_operacao, moeda, valor_me);
CREATE INDEX idx_boleta_timeframe ON boleta(data_operacao DESC, data_criacao DESC);

-- Índice para agregações
CREATE INDEX idx_boleta_aggregations ON boleta(moeda, status, valor_me) 
WHERE valor_me > 0 AND status = 'completed';
```

#### 3.3 Implementar Materialized Views
```sql
-- View materializada para dados agregados por dia
CREATE MATERIALIZED VIEW mv_daily_kpis AS
SELECT 
    DATE(data_operacao) as date,
    moeda,
    COUNT(*) as transaction_count,
    SUM(valor_me) as total_volume,
    AVG(valor_me) as avg_ticket,
    AVG(spread) as avg_spread
FROM boleta
WHERE data_operacao >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(data_operacao), moeda;

-- Refresh diário
CREATE INDEX idx_mv_daily_kpis ON mv_daily_kpis(date, moeda);
```

## 📈 Implementação Progressiva

### Sprint 1 (Dias 1-2): Quick Wins
- [ ] Aumentar TTL do cache para 30min
- [ ] Implementar paralelização básica de queries
- [ ] Ativar uso do snapshot existente

**Meta**: Reduzir tempo de carregamento para < 5s

### Sprint 2 (Dias 3-4): Correções Críticas  
- [ ] Corrigir bug de sincronização de filtros
- [ ] Implementar cache por filtro (timeframe + currency)
- [ ] Adicionar logs de performance

**Meta**: Garantir consistência de dados

### Sprint 3 (Dias 5-7): Otimizações Profundas
- [ ] Criar índices no banco de dados
- [ ] Implementar cache warming
- [ ] Adicionar materialized views

**Meta**: Atingir tempo de carregamento < 2s

## 🧪 Testes de Performance

### Teste Automatizado
```bash
# Executar teste de performance
cd apps/backend
python tests/test_dashboard_performance.py
```

### Métricas a Monitorar
1. **Tempo de resposta da API**: P50, P90, P99
2. **Taxa de cache hit**: Por KPI e filtro
3. **Tempo de query SQL**: Por KPI
4. **Uso de CPU/Memória**: Durante picos

### Critérios de Aceitação
- [ ] Dashboard carrega em < 2s (P90)
- [ ] Filtros atualizam em < 500ms
- [ ] Cache hit rate > 80%
- [ ] Valores e gráficos sempre sincronizados
- [ ] Sem erros de timeout (30s)

## 🚨 Riscos e Mitigações

| Risco | Probabilidade | Impacto | Mitigação |
|-------|--------------|---------|-----------|
| Queries complexas demais | Alta | Alto | Usar materialized views |
| Cache invalidation bugs | Média | Médio | Testes extensivos |
| Overhead de paralelização | Baixa | Baixo | Limitar workers |
| Breaking changes no frontend | Média | Alto | Feature flags |

## 📊 Monitoramento Pós-Implementação

### Dashboard de Métricas
```python
# Endpoint de monitoramento
@router.get("/api/monitoring/dashboard-metrics")
async def get_dashboard_metrics():
    return {
        "avg_load_time_ms": get_avg_load_time(),
        "cache_hit_rate": get_cache_hit_rate(),
        "slow_queries": get_slow_queries(),
        "error_rate": get_error_rate()
    }
```

### Alertas
- Tempo de carregamento > 5s por 5 minutos
- Taxa de erro > 1%
- Cache hit rate < 50%

## ✅ Checklist de Implementação

### Backend
- [ ] Criar `kpi_service_optimized.py` com paralelização
- [ ] Atualizar configuração de cache TTL
- [ ] Implementar cache warming service
- [ ] Criar índices no PostgreSQL
- [ ] Adicionar logs de performance
- [ ] Criar endpoint de monitoramento

### Frontend  
- [ ] Modificar `useKpiData` para usar snapshot
- [ ] Garantir sincronização de filtros
- [ ] Adicionar loading states granulares
- [ ] Implementar retry com backoff
- [ ] Adicionar métricas no browser

### DevOps
- [ ] Configurar monitoramento (Datadog/New Relic)
- [ ] Criar alertas de performance
- [ ] Documentar runbook de troubleshooting

## 🎯 Resultado Esperado

Após implementação completa:
- **Carregamento inicial**: 1.5s (↓ 93%)
- **Mudança de filtros**: 300ms (↓ 98%)
- **Cache hit rate**: 85%
- **Satisfação do usuário**: 📈

---

**Próximos Passos**: Começar pela Fase 1 - Otimizações Imediatas 