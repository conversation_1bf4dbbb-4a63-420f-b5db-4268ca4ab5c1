# Solução Final: Dashboard Filters com Cache Inteligente

**Data**: 15/07/2025  
**Status**: Implementação Parcial  

## 🎯 Estratégia Recomendada

Com base na pesquisa de melhores práticas de 2024/2025, a solução mais elegante e eficiente é:

### **Cache com TTL Dinâmico + Invalidação Seletiva + Cache Hierárquico**

## 1. Por que esta abordagem?

### Vantagens:
- ✅ **Performance**: Mantém dados em cache quando apropriado
- ✅ **Flexibilidade**: Permite invalidação forçada quando necessário
- ✅ **Escalabilidade**: Funciona bem em ambientes distribuídos
- ✅ **Modernidade**: Segue padrões de 2024/2025 para cache com parâmetros dinâmicos

### Implementação em 3 Camadas:

```python
# 1. Cache Keys com Parâmetros (já implementado ✅)
cache_key = f"{client_id}:{kpi_id}:{timeframe}:{currency}"

# 2. TTL Baseado em Volatilidade (implementar)
def get_dynamic_ttl(timeframe: str) -> int:
    ttl_map = {
        '1d': 60,       # 1 minuto - dados muito voláteis
        'week': 300,    # 5 minutos - dados moderados
        'month': 900,   # 15 minutos - dados mais estáveis
        'quarter': 3600 # 1 hora - dados históricos
    }
    return ttl_map.get(timeframe, 300)

# 3. Invalidação Seletiva (implementado ✅)
@router.get("/api/dashboard/kpis")
async def get_dashboard_kpis(
    force_refresh: bool = Query(False, description="Force cache refresh")
):
    if force_refresh:
        kpi_service.invalidate_kpi_cache(priority_kpis, client_id, timeframe, currency)
```

## 2. Problema Identificado

### Cache Hierárquico Complexo:
O sistema tem múltiplas camadas de cache que não estão sincronizadas:

1. **Cache em memória** (`_kpi_cache`)
2. **Cache hierárquico** (`hierarchical_cache`)
3. **Possível cache persistente** (arquivo/Redis)
4. **Cache no KPI Calculator**

### Solução Proposta:

```python
class UnifiedCacheManager:
    """Gerenciador unificado de cache para todas as camadas"""
    
    def __init__(self):
        self.memory_cache = {}
        self.redis_client = get_redis_client()
        self.ttl_strategy = DynamicTTLStrategy()
    
    async def get(self, key: str) -> Optional[Any]:
        # Primeiro tenta memória
        if key in self.memory_cache:
            return self.memory_cache[key]
        
        # Depois Redis
        value = await self.redis_client.get(key)
        if value:
            self.memory_cache[key] = value  # Popula memória
            return value
        
        return None
    
    async def set(self, key: str, value: Any, timeframe: str):
        ttl = self.ttl_strategy.get_ttl(timeframe)
        
        # Salva em ambas as camadas
        self.memory_cache[key] = value
        await self.redis_client.set(key, value, ex=ttl)
    
    async def invalidate(self, pattern: str):
        # Limpa memória
        keys_to_delete = [k for k in self.memory_cache if k.startswith(pattern)]
        for key in keys_to_delete:
            del self.memory_cache[key]
        
        # Limpa Redis
        async for key in self.redis_client.scan_iter(f"{pattern}*"):
            await self.redis_client.delete(key)
```

## 3. Implementação Rápida (Solução Temporária)

Para resolver o problema imediatamente:

```python
# Em kpi_service.py - Desabilitar cache temporariamente para debug
def _calculate_real_kpi_value(self, kpi_id: str, client_id: str, timeframe: str, currency: str):
    # TEMPORÁRIO: Bypass cache para debug
    if os.getenv("DISABLE_KPI_CACHE", "false").lower() == "true":
        logger.warning("⚠️ KPI cache disabled for debugging")
        return self._execute_kpi_calculation(kpi_id, client_id, timeframe, currency)
    
    # Código normal de cache...
```

## 4. Implementação Completa (Produção)

### 4.1 Cache Service Unificado
```python
# src/services/unified_cache_service.py
from typing import Optional, Any
import aioredis
from cachetools import TTLCache
import json

class UnifiedCacheService:
    def __init__(self):
        self.local_cache = TTLCache(maxsize=1000, ttl=300)
        self.redis = aioredis.from_url("redis://localhost")
        
    async def get_kpi_value(
        self, 
        kpi_id: str, 
        client_id: str, 
        timeframe: str, 
        currency: str
    ) -> Optional[float]:
        key = f"kpi:{client_id}:{kpi_id}:{timeframe}:{currency}"
        
        # L1: Local cache
        if key in self.local_cache:
            return self.local_cache[key]
        
        # L2: Redis
        value = await self.redis.get(key)
        if value:
            self.local_cache[key] = float(value)
            return float(value)
            
        return None
```

### 4.2 Frontend Integration
```typescript
// useKpiData.ts
const { data, error, isLoading, mutate } = useSWR(
  `/api/dashboard/kpis?${params}`,
  fetcher,
  {
    refreshInterval: getRefreshInterval(filters.timeframe),
    revalidateOnFocus: false,
    dedupingInterval: 5000
  }
);

// Force refresh
const forceRefresh = () => {
  mutate(`/api/dashboard/kpis?${params}&force_refresh=true`);
};
```

## 5. Monitoramento e Observabilidade

```python
# Métricas de cache
cache_hits = Counter('kpi_cache_hits_total', 'Total cache hits')
cache_misses = Counter('kpi_cache_misses_total', 'Total cache misses')
cache_invalidations = Counter('kpi_cache_invalidations_total', 'Total invalidations')

# Logs estruturados
logger.info("cache_event", {
    "type": "hit|miss|invalidate",
    "kpi_id": kpi_id,
    "timeframe": timeframe,
    "latency_ms": latency
})
```

## 6. Próximos Passos

1. **Imediato**: 
   - [ ] Implementar bypass de cache temporário
   - [ ] Adicionar logs detalhados de cache

2. **Curto Prazo**:
   - [ ] Unificar todas as camadas de cache
   - [ ] Implementar TTL dinâmico
   - [ ] Adicionar métricas

3. **Longo Prazo**:
   - [ ] Migrar para Redis Cluster
   - [ ] Implementar cache warming
   - [ ] Cache invalidation via eventos

## Conclusão

A solução recomendada combina as melhores práticas de 2024/2025:
- **Cache inteligente** com TTL baseado em volatilidade
- **Invalidação seletiva** para controle fino
- **Arquitetura em camadas** para performance
- **Observabilidade** para monitoramento

Esta abordagem garante performance sem sacrificar a atualidade dos dados. 